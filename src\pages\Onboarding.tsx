import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { CheckCircle, ArrowRight, ArrowLeft, Rocket, Target, Palette, Settings } from 'lucide-react';

const Onboarding = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [completed, setCompleted] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const totalSteps = 4;
  const progress = (currentStep / totalSteps) * 100;

  const steps = [
    {
      id: 1,
      title: "Bem-vindo ao CaptaFlow!",
      description: "Vamos configurar sua conta em poucos passos",
      icon: Rocket,
      content: (
        <div className="space-y-4 text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="p-4 bg-luxury-gold/20 rounded-full">
              <Rocket className="w-12 h-12 text-luxury-brown" />
            </div>
          </div>
          <h3 className="text-xl font-semibold text-luxury-brown">
            Parabéns por começar sua jornada!
          </h3>
          <p className="text-luxury-brown/70">
            Seu sistema whitelabel está quase pronto. Vamos personalizar tudo para seu negócio 
            em apenas alguns passos simples.
          </p>
          <div className="bg-luxury-gold/10 p-4 rounded-lg border border-luxury-gold/20">
            <p className="text-sm text-luxury-brown/80">
              <strong>Você está em período de trial de 14 dias</strong><br />
              Aproveite para explorar todas as funcionalidades!
            </p>
          </div>
        </div>
      )
    },
    {
      id: 2,
      title: "Defina seus Objetivos",
      description: "Conte-nos mais sobre o que você pretende alcançar",
      icon: Target,
      content: (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-luxury-brown text-center mb-6">
            Qual é o seu principal objetivo?
          </h3>
          <div className="grid gap-3">
            {[
              "Capturar mais leads para meu negócio",
              "Criar páginas de destino profissionais",
              "Automatizar meu funil de vendas",
              "Expandir minha presença digital",
              "Outros objetivos"
            ].map((objetivo, index) => (
              <Button
                key={index}
                variant="outline"
                className="w-full text-left justify-start p-4 h-auto hover:bg-luxury-gold/10 hover:border-luxury-gold"
                onClick={() => {
                  toast({
                    title: "Objetivo selecionado!",
                    description: objetivo,
                  });
                }}
              >
                <Target className="w-4 h-4 mr-3 text-luxury-gold" />
                {objetivo}
              </Button>
            ))}
          </div>
        </div>
      )
    },
    {
      id: 3,
      title: "Personalize sua Marca",
      description: "Configure as cores e visual da sua página",
      icon: Palette,
      content: (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-luxury-brown text-center mb-6">
            Personalize seu Visual
          </h3>
          <div className="space-y-4">
            <div className="p-4 border border-luxury-gold/20 rounded-lg bg-luxury-beige/10">
              <h4 className="font-medium text-luxury-brown mb-2">📸 Logo da Empresa</h4>
              <p className="text-sm text-luxury-brown/70 mb-3">
                Adicione o logo da sua empresa para personalizar sua página
              </p>
              <Button size="sm" variant="outline">
                Fazer Upload do Logo
              </Button>
            </div>
            
            <div className="p-4 border border-luxury-gold/20 rounded-lg bg-luxury-beige/10">
              <h4 className="font-medium text-luxury-brown mb-2">🎨 Cores da Marca</h4>
              <p className="text-sm text-luxury-brown/70 mb-3">
                Escolha as cores que representam sua marca
              </p>
              <div className="flex gap-2">
                {['#1e40af', '#f59e0b', '#10b981', '#ef4444', '#8b5cf6'].map((color, index) => (
                  <div
                    key={index}
                    className="w-8 h-8 rounded-full border-2 border-white shadow cursor-pointer hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                    onClick={() => {
                      toast({
                        title: "Cor selecionada!",
                        description: `Cor ${color} aplicada`,
                      });
                    }}
                  />
                ))}
              </div>
            </div>

            <div className="p-4 border border-luxury-gold/20 rounded-lg bg-luxury-beige/10">
              <h4 className="font-medium text-luxury-brown mb-2">✏️ Nome da Empresa</h4>
              <p className="text-sm text-luxury-brown/70 mb-3">
                Como sua empresa deve aparecer na página
              </p>
              <Button size="sm" variant="outline">
                Personalizar Textos
              </Button>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 4,
      title: "Configuração Finalizada",
      description: "Tudo pronto! Vamos para o painel administrativo",
      icon: Settings,
      content: (
        <div className="space-y-4 text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="p-4 bg-green-100 rounded-full">
              <CheckCircle className="w-12 h-12 text-green-600" />
            </div>
          </div>
          <h3 className="text-xl font-semibold text-luxury-brown">
            Configuração Concluída!
          </h3>
          <p className="text-luxury-brown/70">
            Perfeito! Sua conta está configurada e pronta para usar. 
            Você já pode começar a capturar leads e gerenciar seu negócio.
          </p>
          
          <div className="grid gap-3 mt-6">
            <div className="p-3 bg-luxury-gold/10 rounded-lg border border-luxury-gold/20">
              <h4 className="font-medium text-luxury-brown">🎯 Próximos Passos</h4>
              <ul className="text-sm text-luxury-brown/70 mt-2 space-y-1">
                <li>• Personalize ainda mais sua página</li>
                <li>• Configure integrações</li>
                <li>• Comece a receber leads</li>
                <li>• Explore relatórios e analytics</li>
              </ul>
            </div>
          </div>
        </div>
      )
    }
  ];

  const currentStepData = steps.find(step => step.id === currentStep);
  const StepIcon = currentStepData?.icon || Rocket;

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      setCompleted(true);
      toast({
        title: "Onboarding concluído!",
        description: "Bem-vindo ao seu painel administrativo.",
      });
      
      // Marcar onboarding como concluído no localStorage
      localStorage.setItem('onboardingCompleted', 'true');
      
      // Redirecionar para o admin após 2 segundos
      setTimeout(() => {
        navigate('/admin');
      }, 2000);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    localStorage.setItem('onboardingCompleted', 'true');
    navigate('/admin');
  };

  if (completed) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center mb-4">
                <div className="p-4 bg-green-100 rounded-full">
                  <CheckCircle className="w-12 h-12 text-green-600" />
                </div>
              </div>
              <h2 className="text-2xl font-bold text-luxury-brown">
                Tudo Pronto!
              </h2>
              <p className="text-luxury-brown/70">
                Redirecionando para seu painel...
              </p>
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-luxury-gold mx-auto"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-luxury-gold/20 rounded-full">
              <StepIcon className="w-8 h-8 text-luxury-brown" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-luxury-brown mb-2">
            {currentStepData?.title}
          </h1>
          <p className="text-luxury-brown/70">
            {currentStepData?.description}
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-luxury-brown/70">
              Passo {currentStep} de {totalSteps}
            </span>
            <span className="text-sm text-luxury-brown/70">
              {Math.round(progress)}% concluído
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Content Card */}
        <Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm mb-8">
          <CardHeader>
            <CardTitle className="text-center text-luxury-brown">
              {currentStepData?.title}
            </CardTitle>
            <CardDescription className="text-center">
              {currentStepData?.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {currentStepData?.content}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <div className="flex gap-2">
            {currentStep > 1 && (
              <Button
                variant="outline"
                onClick={handlePrevious}
                className="flex items-center"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Anterior
              </Button>
            )}
            
            <Button
              variant="ghost"
              onClick={handleSkip}
              className="text-luxury-brown/70 hover:text-luxury-brown"
            >
              Pular Tutorial
            </Button>
          </div>

          <Button
            onClick={handleNext}
            className="bg-gradient-to-r from-luxury-gold to-luxury-gold-dark hover:from-luxury-gold-dark hover:to-luxury-gold text-luxury-brown flex items-center"
          >
            {currentStep === totalSteps ? 'Finalizar' : 'Próximo'}
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Onboarding; 