import { useState } from 'react';
import { Navigate, useNavigate, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/hooks/useAuth';
import { Lock, User, ArrowLeft, Eye, EyeOff, Shield, Building2, Mail } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const AdminLogin = () => {
  const [identifier, setIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loginType, setLoginType] = useState<'username' | 'email'>('username');
  const { isAuthenticated, login } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Se já estiver autenticado, redireciona para o admin
  if (isAuthenticated) {
    return <Navigate to="/admin" replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      await login(identifier, password, loginType === 'email');
      
      toast({
        title: "Login realizado com sucesso!",
        description: "Bem-vindo ao painel administrativo.",
      });

      // Verificar se precisa de onboarding
      const onboardingCompleted = localStorage.getItem('onboardingCompleted') === 'true';
      
      // Redirecionar baseado no status do onboarding
      if (!onboardingCompleted) {
        navigate('/onboarding');
      } else {
        navigate('/admin');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Erro no login';
      setError(errorMessage);
      
      // Verificar se é erro específico de verificação de email
      if (errorMessage.includes('pendente de verificação')) {
        toast({
          title: "Email não verificado",
          description: "Verifique seu email para ativar sua conta.",
          variant: "destructive",
        });
        
        // Mostrar link para reenviar verificação
        setTimeout(() => {
          if (confirm('Não recebeu o email de verificação? Clique OK para reenviar.')) {
            navigate('/verify-email');
          }
        }, 2000);
      } else if (errorMessage.includes('expirada') || errorMessage.includes('suspensa')) {
        toast({
          title: "Acesso Restrito",
          description: errorMessage,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Erro no login",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    navigate('/forgot-password');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full bg-gradient-to-br from-luxury-gold/20 to-transparent"></div>
      </div>
      
      <div className="relative w-full max-w-md mx-auto z-10">
        {/* Header Section */}
        <div className="mb-6 sm:mb-8 text-center">
          <Link to="/">
            <Button 
              variant="ghost" 
              size="sm" 
              className="mb-4 sm:mb-6 text-luxury-brown hover:text-luxury-gold hover:bg-luxury-gold/10 transition-all duration-300"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar ao Site
            </Button>
          </Link>
          
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-luxury-gold/20 rounded-full">
              <Building2 className="w-8 h-8 text-luxury-brown" />
            </div>
          </div>
          
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-luxury-brown mb-2">
            Painel Administrativo
          </h1>
          <p className="text-luxury-brown/70 text-sm sm:text-base">
            Sistema WhiteLabel CaptaFlow
          </p>
        </div>

        {/* Login Card */}
        <Card className="w-full shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="space-y-2 pb-4">
            <CardTitle className="text-xl sm:text-2xl text-center flex items-center justify-center text-luxury-brown">
              <Shield className="w-5 h-5 sm:w-6 sm:h-6 mr-2 text-luxury-gold" />
              Acesso Seguro
            </CardTitle>
            <CardDescription className="text-center text-luxury-brown/70 text-sm sm:text-base">
              Digite suas credenciais para acessar o sistema
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pt-0">
            {/* Login Type Toggle */}
            <div className="flex mb-6 bg-luxury-beige/20 rounded-lg p-1">
              <Button
                type="button"
                variant={loginType === 'username' ? 'default' : 'ghost'}
                size="sm"
                className={`flex-1 text-xs ${
                  loginType === 'username' 
                    ? 'bg-luxury-gold text-luxury-brown hover:bg-luxury-gold-dark' 
                    : 'text-luxury-brown hover:bg-luxury-gold/20'
                }`}
                onClick={() => setLoginType('username')}
              >
                <User className="w-3 h-3 mr-1" />
                Usuário
              </Button>
              <Button
                type="button"
                variant={loginType === 'email' ? 'default' : 'ghost'}
                size="sm"
                className={`flex-1 text-xs ${
                  loginType === 'email' 
                    ? 'bg-luxury-gold text-luxury-brown hover:bg-luxury-gold-dark' 
                    : 'text-luxury-brown hover:bg-luxury-gold/20'
                }`}
                onClick={() => setLoginType('email')}
              >
                <Mail className="w-3 h-3 mr-1" />
                Email
              </Button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
              {/* Identifier Field */}
              <div className="space-y-2">
                <Label 
                  htmlFor="identifier" 
                  className="text-luxury-brown font-medium text-sm sm:text-base"
                >
                  {loginType === 'email' ? 'Email' : 'Usuário'}
                </Label>
                <div className="relative">
                  {loginType === 'email' ? (
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-luxury-brown/50" />
                  ) : (
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-luxury-brown/50" />
                  )}
                  <Input
                    id="identifier"
                    type={loginType === 'email' ? 'email' : 'text'}
                    placeholder={loginType === 'email' ? 'Digite seu email' : 'Digite seu usuário'}
                    value={identifier}
                    onChange={(e) => setIdentifier(e.target.value)}
                    className="pl-10 sm:pl-12 h-11 sm:h-12 text-sm sm:text-base bg-luxury-beige/20 border-luxury-gold/30 focus:border-luxury-gold focus:ring-luxury-gold/20 rounded-xl"
                    required
                    disabled={loading}
                  />
                </div>
              </div>
              
              {/* Password Field */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label 
                    htmlFor="password" 
                    className="text-luxury-brown font-medium text-sm sm:text-base"
                  >
                    Senha
                  </Label>
                  <Button
                    type="button"
                    variant="link"
                    size="sm"
                    className="text-xs text-luxury-gold hover:text-luxury-gold-dark p-0 h-auto"
                    onClick={handleForgotPassword}
                  >
                    Esqueceu a senha?
                  </Button>
                </div>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-luxury-brown/50" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Digite sua senha"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10 sm:pl-12 pr-10 sm:pr-12 h-11 sm:h-12 text-sm sm:text-base bg-luxury-beige/20 border-luxury-gold/30 focus:border-luxury-gold focus:ring-luxury-gold/20 rounded-xl"
                    required
                    disabled={loading}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-luxury-gold/10"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-luxury-brown/50" />
                    ) : (
                      <Eye className="h-4 w-4 text-luxury-brown/50" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <Alert variant="destructive" className="border-red-200 bg-red-50">
                  <AlertDescription className="text-sm">{error}</AlertDescription>
                </Alert>
              )}

              {/* Submit Button */}
              <Button 
                type="submit" 
                className="w-full h-11 sm:h-12 text-sm sm:text-base font-semibold bg-gradient-to-r from-luxury-gold to-luxury-gold-dark hover:from-luxury-gold-dark hover:to-luxury-gold text-luxury-brown shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-luxury-brown mr-2"></div>
                    Entrando...
                  </div>
                ) : (
                  'Entrar no Sistema'
                )}
              </Button>
            </form>

            {/* Credentials Info */}
            <div className="mt-6 p-3 sm:p-4 bg-gradient-to-r from-luxury-gold/10 to-luxury-beige/20 rounded-xl border border-luxury-gold/20">
              <p className="text-xs sm:text-sm text-luxury-brown/80 text-center leading-relaxed">
                <strong className="text-luxury-brown">Credenciais de acesso:</strong><br />
                <span className="font-mono bg-white/50 px-2 py-1 rounded mt-1 inline-block">
                  Usuário: admin | Email: <EMAIL>
                </span><br />
                <span className="font-mono bg-white/50 px-2 py-1 rounded mt-1 inline-block">
                  Senha: admin123
                </span>
              </p>
            </div>

            {/* Additional Links */}
            <div className="mt-4 text-center">
              <p className="text-xs text-luxury-brown/60">
                Novo por aqui?{' '}
                <Link to="/register" className="text-luxury-gold hover:text-luxury-gold-dark font-medium">
                  Cadastre sua empresa
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminLogin;