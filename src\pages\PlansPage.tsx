import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { toast } from 'sonner';
import { Check, CreditCard, QrCode, Crown, Star, Zap } from 'lucide-react';

interface Plan {
  id: number;
  name: string;
  price: number;
  max_campaigns: number;
  max_leads_per_month: number;
  features: string[];
}

const PlansPage: React.FC = () => {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingPayment, setProcessingPayment] = useState<number | null>(null);
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/plans');
      const data = await response.json();
      
      if (data.success) {
        setPlans(data.data.map((plan: any) => ({
          ...plan,
          features: JSON.parse(plan.features || '[]')
        })));
      }
    } catch (error) {
      console.error('Erro ao buscar planos:', error);
      toast.error('Erro ao carregar planos');
    } finally {
      setLoading(false);
    }
  };

  const handleStripePayment = async (planId: number) => {
    setProcessingPayment(planId);
    
    try {
      const response = await fetch('/api/payments/stripe/create-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          plan_id: planId,
          success_url: `${window.location.origin}/admin?payment=success`,
          cancel_url: `${window.location.origin}/plans?payment=cancelled`
        })
      });

      const data = await response.json();
      
      if (data.success) {
        // Redirecionar para o Stripe Checkout
        window.location.href = data.checkout_url;
      } else {
        toast.error(data.error || 'Erro ao processar pagamento');
      }
    } catch (error) {
      console.error('Erro ao processar pagamento Stripe:', error);
      toast.error('Erro ao processar pagamento');
    } finally {
      setProcessingPayment(null);
    }
  };

  const handlePixPayment = async (planId: number) => {
    setProcessingPayment(planId);
    
    try {
      const response = await fetch('/api/payments/pix/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ plan_id: planId })
      });

      const data = await response.json();
      
      if (data.success) {
        // Navegar para página de pagamento PIX
        navigate('/payment/pix', { 
          state: { 
            pixData: data.data,
            planName: plans.find(p => p.id === planId)?.name 
          } 
        });
      } else {
        toast.error(data.error || 'Erro ao gerar PIX');
      }
    } catch (error) {
      console.error('Erro ao gerar PIX:', error);
      toast.error('Erro ao gerar PIX');
    } finally {
      setProcessingPayment(null);
    }
  };

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'essencial':
        return <Star className="h-8 w-8 text-blue-500" />;
      case 'growth':
        return <Zap className="h-8 w-8 text-green-500" />;
      case 'agency':
        return <Crown className="h-8 w-8 text-purple-500" />;
      default:
        return <Star className="h-8 w-8 text-gray-500" />;
    }
  };

  const getPlanColor = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'essencial':
        return 'border-blue-200 bg-blue-50';
      case 'growth':
        return 'border-green-200 bg-green-50';
      case 'agency':
        return 'border-purple-200 bg-purple-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const isPopular = (planName: string) => planName.toLowerCase() === 'growth';

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando planos...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Escolha seu plano
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comece com 14 dias grátis. Cancele a qualquer momento.
            Todos os planos incluem suporte técnico e atualizações gratuitas.
          </p>
        </div>

        {/* Planos */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative rounded-2xl shadow-lg overflow-hidden transition-all duration-200 hover:shadow-xl ${getPlanColor(plan.name)} ${
                isPopular(plan.name) ? 'ring-2 ring-green-500 transform scale-105' : ''
              }`}
            >
              {/* Badge Popular */}
              {isPopular(plan.name) && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <span className="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Mais Popular
                  </span>
                </div>
              )}

              <div className="p-8">
                {/* Header do Plano */}
                <div className="text-center mb-8">
                  <div className="flex justify-center mb-4">
                    {getPlanIcon(plan.name)}
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  <div className="text-4xl font-bold text-gray-900 mb-1">
                    R$ {plan.price.toFixed(0)}
                  </div>
                  <p className="text-gray-600">por mês</p>
                </div>

                {/* Features */}
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* Botões de Pagamento */}
                <div className="space-y-3">
                  {/* Botão Stripe */}
                  <button
                    onClick={() => handleStripePayment(plan.id)}
                    disabled={processingPayment === plan.id}
                    className={`w-full flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                      isPopular(plan.name)
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                  >
                    <CreditCard className="h-5 w-5 mr-2" />
                    {processingPayment === plan.id ? 'Processando...' : 'Pagar com Cartão'}
                  </button>

                  {/* Botão PIX */}
                  <button
                    onClick={() => handlePixPayment(plan.id)}
                    disabled={processingPayment === plan.id}
                    className="w-full flex items-center justify-center px-6 py-3 border-2 border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <QrCode className="h-5 w-5 mr-2" />
                    Pagar com PIX
                  </button>
                </div>

                {/* Limite de leads */}
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-600 text-center">
                    {plan.max_campaigns === -1 ? 'Campanhas ilimitadas' : `${plan.max_campaigns} campanha(s)`}
                    {' • '}
                    {plan.max_leads_per_month === -1 ? 'Leads ilimitados' : `${plan.max_leads_per_month} leads/mês`}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Garantia */}
        <div className="text-center">
          <div className="inline-flex items-center px-6 py-3 rounded-full bg-white shadow-md">
            <Check className="h-5 w-5 text-green-500 mr-2" />
            <span className="text-gray-700 font-medium">
              Garantia de 30 dias - 100% do seu dinheiro de volta
            </span>
          </div>
        </div>

        {/* FAQ Resumido */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
            Perguntas Frequentes
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg p-6 shadow-md">
              <h4 className="font-semibold text-gray-900 mb-2">
                Posso cancelar a qualquer momento?
              </h4>
              <p className="text-gray-600">
                Sim, você pode cancelar sua assinatura a qualquer momento sem taxas adicionais.
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-md">
              <h4 className="font-semibold text-gray-900 mb-2">
                O trial é realmente gratuito?
              </h4>
              <p className="text-gray-600">
                Sim, 14 dias completamente gratuitos. Não cobramos nada até o final do período.
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-md">
              <h4 className="font-semibold text-gray-900 mb-2">
                Posso mudar de plano depois?
              </h4>
              <p className="text-gray-600">
                Claro! Você pode fazer upgrade ou downgrade do seu plano a qualquer momento.
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-md">
              <h4 className="font-semibold text-gray-900 mb-2">
                Há suporte técnico incluído?
              </h4>
              <p className="text-gray-600">
                Todos os planos incluem suporte por email. Planos superiores têm suporte prioritário.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlansPage; 