const bcrypt = require('bcryptjs');
const { db } = require('./database.cjs');

const createSuperAdmin = async () => {
  try {
    console.log('🔐 Criando usuário Super Admin do CaptaFlow...');
    
    // Verificar se já existe
    const existingUser = db.prepare('SELECT * FROM users WHERE username = ?').get('captaflow_admin');
    
    if (existingUser) {
      console.log('✅ Usuário Super Admin já existe!');
      console.log(`- Username: captaflow_admin`);
      console.log(`- Role: ${existingUser.role}`);
      return;
    }
    
    // Criar novo usuário super admin
    const hashedPassword = await bcrypt.hash('captaflow2024!', 10);
    const stmt = db.prepare(`
      INSERT INTO users (username, password, email, role, company_id, status)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      'captaflow_admin',
      hashedPassword,
      '<EMAIL>',
      'super_admin',
      null,
      'active'
    );
    
    if (result.changes > 0) {
      console.log('🎉 Usuário Super Admin criado com sucesso!');
      console.log('');
      console.log('🔑 Credenciais de acesso:');
      console.log('- Username: captaflow_admin');
      console.log('- Password: captaflow2024!');
      console.log('- Role: super_admin');
      console.log('- Email: <EMAIL>');
      console.log('');
      console.log('⚠️  IMPORTANTE: Guarde essas credenciais em local seguro!');
    } else {
      console.log('❌ Erro ao criar usuário Super Admin');
    }
    
  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
};

createSuperAdmin().then(() => {
  console.log('✅ Script finalizado');
  process.exit(0);
}).catch(error => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
}); 