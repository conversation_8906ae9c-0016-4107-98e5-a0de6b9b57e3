import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { 
  QrCode, 
  Copy, 
  Upload, 
  CheckCircle, 
  AlertCircle, 
  ArrowLeft,
  Clock,
  FileText
} from 'lucide-react';

interface PixData {
  transaction_id: number;
  pix_code: string;
  qr_code: string;
  amount: number;
  pix_key: string;
  description: string;
}

const PixPaymentPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { pixData, planName } = location.state || {};
  
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'uploaded' | 'approved'>('pending');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    if (!pixData) {
      toast.error('Dados do pagamento não encontrados');
      navigate('/plans');
    }
  }, [pixData, navigate]);

  const copyPixCode = () => {
    navigator.clipboard.writeText(pixData.pix_code);
    toast.success('Código PIX copiado para a área de transferência!');
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validar tipo de arquivo
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'];
      if (!validTypes.includes(file.type)) {
        toast.error('Formato de arquivo não suportado. Use JPG, PNG, GIF ou PDF.');
        return;
      }

      // Validar tamanho (máximo 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Arquivo muito grande. Máximo 5MB.');
        return;
      }

      setUploadedFile(file);
      
      // Criar preview se for imagem
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => setPreviewUrl(e.target?.result as string);
        reader.readAsDataURL(file);
      } else {
        setPreviewUrl(null);
      }
    }
  };

  const uploadReceipt = async () => {
    if (!uploadedFile) {
      toast.error('Selecione um comprovante primeiro');
      return;
    }

    setUploading(true);

    try {
      // Simular upload (em produção, você usaria um serviço como AWS S3, Cloudinary, etc.)
      const formData = new FormData();
      formData.append('file', uploadedFile);
      
      // Por agora, vamos simular o upload e usar uma URL fictícia
      await new Promise(resolve => setTimeout(resolve, 2000));
      const mockReceiptUrl = `https://mockcdn.com/receipts/${Date.now()}_${uploadedFile.name}`;

      // Enviar comprovante para o servidor
      const response = await fetch('/api/payments/pix/upload-receipt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          transaction_id: pixData.transaction_id,
          receipt_url: mockReceiptUrl
        })
      });

      const data = await response.json();

      if (data.success) {
        setPaymentStatus('uploaded');
        toast.success('Comprovante enviado com sucesso! Aguarde aprovação.');
      } else {
        toast.error(data.error || 'Erro ao enviar comprovante');
      }
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      toast.error('Erro ao enviar comprovante');
    } finally {
      setUploading(false);
    }
  };

  const downloadQRCode = () => {
    const link = document.createElement('a');
    link.download = `pix-qrcode-${planName}.png`;
    link.href = pixData.qr_code;
    link.click();
  };

  if (!pixData) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/plans')}
            className="flex items-center text-blue-600 hover:text-blue-700 mb-4"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Voltar aos planos
          </button>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Pagamento via PIX
          </h1>
          <p className="text-gray-600">
            Plano {planName} - R$ {pixData.amount.toFixed(2)}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* QR Code e Instruções */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              1. Escaneie o QR Code
            </h2>

            {/* QR Code */}
            <div className="text-center mb-6">
              <div className="inline-block p-4 bg-white rounded-lg shadow-inner">
                <img 
                  src={pixData.qr_code} 
                  alt="QR Code PIX" 
                  className="w-64 h-64 mx-auto"
                />
              </div>
              <button
                onClick={downloadQRCode}
                className="mt-4 text-blue-600 hover:text-blue-700 text-sm underline"
              >
                Baixar QR Code
              </button>
            </div>

            {/* Chave PIX */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ou use a chave PIX:
              </label>
              <div className="flex">
                <input
                  type="text"
                  value={pixData.pix_key}
                  readOnly
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-l-lg bg-gray-50 text-sm font-mono"
                />
                <button
                  onClick={copyPixCode}
                  className="px-4 py-3 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors"
                >
                  <Copy className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Valor */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-1">Valor a pagar:</p>
                <p className="text-3xl font-bold text-gray-900">
                  R$ {pixData.amount.toFixed(2)}
                </p>
              </div>
            </div>

            {/* Instruções */}
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">
                  1
                </span>
                <p>Abra o app do seu banco e acesse a área PIX</p>
              </div>
              <div className="flex items-start">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">
                  2
                </span>
                <p>Escaneie o QR Code ou cole a chave PIX</p>
              </div>
              <div className="flex items-start">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">
                  3
                </span>
                <p>Confirme o pagamento de R$ {pixData.amount.toFixed(2)}</p>
              </div>
              <div className="flex items-start">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">
                  4
                </span>
                <p>Envie o comprovante na área ao lado</p>
              </div>
            </div>
          </div>

          {/* Upload do Comprovante */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              2. Envie o Comprovante
            </h2>

            {paymentStatus === 'pending' && (
              <div className="space-y-6">
                {/* Upload Area */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
                  <input
                    type="file"
                    id="receipt-upload"
                    accept="image/*,.pdf"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  <label
                    htmlFor="receipt-upload"
                    className="cursor-pointer block"
                  >
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-lg font-medium text-gray-900 mb-2">
                      Clique para enviar o comprovante
                    </p>
                    <p className="text-gray-600 text-sm">
                      JPG, PNG, GIF ou PDF até 5MB
                    </p>
                  </label>
                </div>

                {/* Preview do arquivo */}
                {uploadedFile && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center">
                        <FileText className="h-6 w-6 text-blue-600 mr-2" />
                        <div>
                          <p className="font-medium text-gray-900">
                            {uploadedFile.name}
                          </p>
                          <p className="text-sm text-gray-600">
                            {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Preview da imagem */}
                    {previewUrl && (
                      <div className="mb-4">
                        <img
                          src={previewUrl}
                          alt="Preview do comprovante"
                          className="max-w-full h-48 object-contain rounded-lg border mx-auto"
                        />
                      </div>
                    )}

                    {/* Botão de upload */}
                    <button
                      onClick={uploadReceipt}
                      disabled={uploading}
                      className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {uploading ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          Enviando...
                        </div>
                      ) : (
                        'Enviar Comprovante'
                      )}
                    </button>
                  </div>
                )}
              </div>
            )}

            {paymentStatus === 'uploaded' && (
              <div className="text-center space-y-6">
                <div className="flex justify-center">
                  <CheckCircle className="h-16 w-16 text-green-500" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    Comprovante Enviado!
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Seu comprovante foi enviado com sucesso. 
                    Nossa equipe irá analisar e aprovar seu pagamento em até 2 horas úteis.
                  </p>
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <Clock className="h-5 w-5 text-amber-600 mr-2" />
                      <span className="text-amber-800 font-medium">
                        Aguardando aprovação
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {paymentStatus === 'approved' && (
              <div className="text-center space-y-6">
                <div className="flex justify-center">
                  <CheckCircle className="h-16 w-16 text-green-500" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    Pagamento Aprovado!
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Seu pagamento foi aprovado e sua conta já está ativa.
                    Você pode acessar todas as funcionalidades do seu plano.
                  </p>
                  <button
                    onClick={() => navigate('/admin')}
                    className="bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Ir para o Dashboard
                  </button>
                </div>
              </div>
            )}

            {/* Informações importantes */}
            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
                <div className="text-sm">
                  <p className="font-medium text-blue-900 mb-1">
                    Importante:
                  </p>
                  <ul className="text-blue-800 space-y-1">
                    <li>• O comprovante deve ser legível e mostrar o valor exato</li>
                    <li>• Pagamentos são processados em até 2 horas úteis</li>
                    <li>• Em caso de dúvidas, entre em contato: (21) 98230-1476</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Status do Pagamento */}
        <div className="mt-8 bg-white rounded-2xl shadow-lg p-8">
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            Status do Pagamento
          </h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <span className="ml-2 text-sm font-medium text-gray-900">
                PIX Gerado
              </span>
            </div>
            <div className="flex-1 h-0.5 bg-gray-300"></div>
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                paymentStatus === 'uploaded' || paymentStatus === 'approved' 
                  ? 'bg-green-500' 
                  : 'bg-gray-300'
              }`}>
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <span className="ml-2 text-sm font-medium text-gray-900">
                Comprovante Enviado
              </span>
            </div>
            <div className="flex-1 h-0.5 bg-gray-300"></div>
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                paymentStatus === 'approved' ? 'bg-green-500' : 'bg-gray-300'
              }`}>
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <span className="ml-2 text-sm font-medium text-gray-900">
                Pagamento Aprovado
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PixPaymentPage; 