import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { toast } from 'sonner';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  CreditCard, 
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Download,
  Filter
} from 'lucide-react';

interface FinancialData {
  totalRevenue: number;
  monthlyRevenue: number;
  clientsByPlan: Array<{ name: string; count: number }>;
  pendingPayments: { count: number; total_amount: number };
  recentTransactions: Array<{
    id: number;
    amount: number;
    status: string;
    payment_method: string;
    company_name: string;
    plan_name: string;
    created_at: string;
    paid_at?: string;
  }>;
}

interface PendingPayment {
  id: number;
  amount: number;
  company_name: string;
  company_email: string;
  plan_name: string;
  payment_method: string;
  pix_receipt_url?: string;
  created_at: string;
  status: string;
}

const FinancialDashboard: React.FC = () => {
  const [financialData, setFinancialData] = useState<FinancialData | null>(null);
  const [pendingPayments, setPendingPayments] = useState<PendingPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingPayment, setProcessingPayment] = useState<number | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('current_month');
  const { user } = useAuth();

  useEffect(() => {
    if (user?.role === 'super_admin') {
      fetchFinancialData();
      fetchPendingPayments();
    }
  }, [user]);

  const fetchFinancialData = async () => {
    try {
      const response = await fetch('/api/admin/financial-dashboard', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setFinancialData(data.data);
      } else {
        toast.error('Erro ao carregar dados financeiros');
      }
    } catch (error) {
      console.error('Erro ao buscar dados financeiros:', error);
      toast.error('Erro ao carregar dashboard financeiro');
    }
  };

  const fetchPendingPayments = async () => {
    try {
      const response = await fetch('/api/admin/payments/pending', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setPendingPayments(data.data);
      }
    } catch (error) {
      console.error('Erro ao buscar pagamentos pendentes:', error);
      toast.error('Erro ao carregar pagamentos pendentes');
    } finally {
      setLoading(false);
    }
  };

  const approvePixPayment = async (transactionId: number) => {
    setProcessingPayment(transactionId);

    try {
      const response = await fetch('/api/admin/payments/pix/approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ transaction_id: transactionId })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Pagamento PIX aprovado com sucesso!');
        fetchPendingPayments();
        fetchFinancialData();
      } else {
        toast.error(data.error || 'Erro ao aprovar pagamento');
      }
    } catch (error) {
      console.error('Erro ao aprovar pagamento:', error);
      toast.error('Erro ao processar aprovação');
    } finally {
      setProcessingPayment(null);
    }
  };

  const rejectPixPayment = async (transactionId: number) => {
    const reason = prompt('Motivo da rejeição (opcional):');
    
    setProcessingPayment(transactionId);

    try {
      const response = await fetch('/api/admin/payments/pix/reject', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ 
          transaction_id: transactionId,
          reason: reason || 'Comprovante inválido'
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Pagamento PIX rejeitado');
        fetchPendingPayments();
      } else {
        toast.error(data.error || 'Erro ao rejeitar pagamento');
      }
    } catch (error) {
      console.error('Erro ao rejeitar pagamento:', error);
      toast.error('Erro ao processar rejeição');
    } finally {
      setProcessingPayment(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { icon: Clock, color: 'bg-yellow-100 text-yellow-800', text: 'Pendente' },
      pending_approval: { icon: AlertCircle, color: 'bg-orange-100 text-orange-800', text: 'Aguardando Aprovação' },
      paid: { icon: CheckCircle, color: 'bg-green-100 text-green-800', text: 'Pago' },
      rejected: { icon: XCircle, color: 'bg-red-100 text-red-800', text: 'Rejeitado' },
      failed: { icon: XCircle, color: 'bg-red-100 text-red-800', text: 'Falhou' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.text}
      </span>
    );
  };

  if (user?.role !== 'super_admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Acesso Negado</h2>
          <p className="text-gray-600">Você não tem permissão para acessar esta página.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando dashboard financeiro...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Dashboard Financeiro
          </h1>
          <p className="text-gray-600">
            Visão completa das métricas financeiras e pagamentos do CaptaFlow
          </p>
        </div>

        {/* Cards de Métricas */}
        {financialData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Receita Total */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Receita Total</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(financialData.totalRevenue)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </div>

            {/* Receita Mensal */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Receita do Mês</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(financialData.monthlyRevenue)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </div>

            {/* Clientes Ativos */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Clientes Ativos</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {financialData.clientsByPlan.reduce((acc, plan) => acc + plan.count, 0)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </div>

            {/* Pagamentos Pendentes */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pendentes</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {financialData.pendingPayments.count}
                  </p>
                  <p className="text-sm text-gray-600">
                    {formatCurrency(financialData.pendingPayments.total_amount)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Clock className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Pagamentos Pendentes de Aprovação */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900">
                    Pagamentos PIX Pendentes
                  </h2>
                  <span className="bg-orange-100 text-orange-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
                    {pendingPayments.filter(p => p.status === 'pending_approval').length} pendentes
                  </span>
                </div>
              </div>

              <div className="divide-y divide-gray-200">
                {pendingPayments.filter(p => p.status === 'pending_approval').length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                    <p>Nenhum pagamento PIX pendente de aprovação</p>
                  </div>
                ) : (
                  pendingPayments
                    .filter(p => p.status === 'pending_approval')
                    .map((payment) => (
                      <div key={payment.id} className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-semibold text-gray-900">
                                {payment.company_name}
                              </h3>
                              {getStatusBadge(payment.status)}
                            </div>
                            
                            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                              <div>
                                <span className="font-medium">Plano:</span> {payment.plan_name}
                              </div>
                              <div>
                                <span className="font-medium">Valor:</span> {formatCurrency(payment.amount)}
                              </div>
                              <div>
                                <span className="font-medium">Email:</span> {payment.company_email}
                              </div>
                              <div>
                                <span className="font-medium">Data:</span> {formatDate(payment.created_at)}
                              </div>
                            </div>

                            {payment.pix_receipt_url && (
                              <div className="mb-4">
                                <a
                                  href={payment.pix_receipt_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm"
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  Ver Comprovante
                                </a>
                              </div>
                            )}

                            <div className="flex space-x-3">
                              <button
                                onClick={() => approvePixPayment(payment.id)}
                                disabled={processingPayment === payment.id}
                                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                              >
                                {processingPayment === payment.id ? 'Processando...' : 'Aprovar'}
                              </button>
                              <button
                                onClick={() => rejectPixPayment(payment.id)}
                                disabled={processingPayment === payment.id}
                                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                              >
                                Rejeitar
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                )}
              </div>
            </div>
          </div>

          {/* Sidebar - Distribuição por Planos e Transações Recentes */}
          <div className="space-y-6">
            {/* Distribuição por Planos */}
            {financialData && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">
                  Clientes por Plano
                </h3>
                <div className="space-y-3">
                  {financialData.clientsByPlan.map((plan, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-gray-700">{plan.name}</span>
                      <span className="font-semibold text-gray-900">{plan.count}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Transações Recentes */}
            {financialData && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">
                  Transações Recentes
                </h3>
                <div className="space-y-4">
                  {financialData.recentTransactions.slice(0, 5).map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900 text-sm">
                          {transaction.company_name}
                        </p>
                        <p className="text-xs text-gray-600">
                          {transaction.plan_name} • {transaction.payment_method.toUpperCase()}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900 text-sm">
                          {formatCurrency(transaction.amount)}
                        </p>
                        {getStatusBadge(transaction.status)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Ações Rápidas */}
            <div className="bg-blue-50 rounded-xl border border-blue-200 p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">
                Ações Rápidas
              </h3>
              <div className="space-y-3">
                <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                  Exportar Relatório
                </button>
                <button className="w-full border border-blue-300 text-blue-700 py-2 px-4 rounded-lg hover:bg-blue-50 transition-colors text-sm">
                  Configurar Alertas
                </button>
                <button className="w-full border border-blue-300 text-blue-700 py-2 px-4 rounded-lg hover:bg-blue-50 transition-colors text-sm">
                  Ver Análise Completa
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Histórico Completo */}
        {financialData && (
          <div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-gray-900">
                  Todas as Transações
                </h2>
                <div className="flex items-center space-x-3">
                  <select 
                    value={selectedPeriod}
                    onChange={(e) => setSelectedPeriod(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  >
                    <option value="current_month">Mês Atual</option>
                    <option value="last_month">Mês Passado</option>
                    <option value="last_3_months">Últimos 3 Meses</option>
                    <option value="all">Todos os Períodos</option>
                  </select>
                  <button className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm">
                    <Download className="h-4 w-4 mr-2" />
                    Exportar
                  </button>
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cliente
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plano
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Valor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Método
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {financialData.recentTransactions.map((transaction) => (
                    <tr key={transaction.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {transaction.company_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {transaction.plan_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                        {formatCurrency(transaction.amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {transaction.payment_method.toUpperCase()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(transaction.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {formatDate(transaction.paid_at || transaction.created_at)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FinancialDashboard; 