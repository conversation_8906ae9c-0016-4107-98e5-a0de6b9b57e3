import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { ReactNode } from 'react';

interface ProtectedRouteProps {
  children: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { isAuthenticated, loading, user } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-luxury-gold mx-auto mb-4"></div>
          <p className="text-luxury-brown/70">Carregando...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/admin-login" replace />;
  }

  // Verificar se precisa de onboarding (apenas para usuários não super admin)
  const isOnboardingRoute = location.pathname === '/onboarding';
  const onboardingCompleted = localStorage.getItem('onboardingCompleted') === 'true';
  const isNewUser = user?.role === 'cliente_admin' && !onboardingCompleted;

  // Se é um novo usuário e não está na rota de onboarding, redirecionar
  if (isNewUser && !isOnboardingRoute) {
    return <Navigate to="/onboarding" replace />;
  }

  // Se já completou onboarding e está tentando acessar onboarding, redirecionar para admin
  if (onboardingCompleted && isOnboardingRoute) {
    return <Navigate to="/admin" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;