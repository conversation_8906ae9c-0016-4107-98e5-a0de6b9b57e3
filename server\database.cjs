const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');
const path = require('path');

// Usar banco em memória no ambiente de produção para evitar problemas de build
const dbPath = process.env.NODE_ENV === 'production' ? ':memory:' : path.join(__dirname, 'database.sqlite');
const db = new Database(dbPath);

// Configurar WAL mode para melhor performance
db.pragma('journal_mode = WAL');

// Criar tabelas
const createTables = () => {
  // Tabela de usuários multi-tenant
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      email TEXT UNIQUE,
      role TEXT DEFAULT 'admin', -- super_admin, cliente_admin, cliente_user
      company_id INTEGER DEFAULT NULL,
      status TEXT DEFAULT 'active', -- active, suspended, pending
      last_login DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (company_id) REFERENCES companies (id)
    )
  `);

  // Tabela de submissões do formulário de contato (multi-tenant)
  db.exec(`
    CREATE TABLE IF NOT EXISTS form_submissions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      company_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      email TEXT NOT NULL,
      phone TEXT,
      message TEXT,
      status TEXT DEFAULT 'pending',
      source_page TEXT,
      user_agent TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (company_id) REFERENCES companies (id)
    )
  `);

  // Tabela para gerenciar conteúdo do site (imagens, textos, etc)
  db.exec(`
    CREATE TABLE IF NOT EXISTS content_management (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      section TEXT NOT NULL,
      type TEXT NOT NULL,
      content TEXT,
      image_url TEXT,
      position INTEGER DEFAULT 0,
      active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Tabela para gerenciar vídeos (multi-tenant)
  db.exec(`
    CREATE TABLE IF NOT EXISTS videos (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      company_id INTEGER DEFAULT NULL,
      title TEXT NOT NULL,
      description TEXT,
      url TEXT NOT NULL,
      thumbnail_url TEXT,
      category TEXT DEFAULT 'general',
      position INTEGER DEFAULT 0,
      active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (company_id) REFERENCES companies (id),
      UNIQUE(url, company_id)
    )
  `);

  // Tabela para configurações gerais do site
  db.exec(`
    CREATE TABLE IF NOT EXISTS site_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      key TEXT UNIQUE NOT NULL,
      value TEXT,
      description TEXT,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Tabela para galeria de imagens (multi-tenant)
  db.exec(`
    CREATE TABLE IF NOT EXISTS gallery_images (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      company_id INTEGER DEFAULT NULL,
      title TEXT NOT NULL,
      description TEXT,
      image_url TEXT NOT NULL,
      order_index INTEGER DEFAULT 0,
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (company_id) REFERENCES companies (id),
      UNIQUE(image_url, company_id)
    )
  `);

  // Atualizar tabela de conteúdo para incluir gerenciamento do rodapé
  db.exec(`
    CREATE TABLE IF NOT EXISTS content_management (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      section TEXT NOT NULL,
      field_name TEXT NOT NULL,
      field_value TEXT,
      company_id INTEGER DEFAULT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(section, field_name, company_id)
    )
  `);

  // NOVAS TABELAS PARA SISTEMA SAAS WHITELABEL

  // Tabela de planos disponíveis
  db.exec(`
    CREATE TABLE IF NOT EXISTS plans (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      price DECIMAL(10,2) NOT NULL,
      currency TEXT DEFAULT 'BRL',
      max_campaigns INTEGER DEFAULT 1,
      max_leads_per_month INTEGER DEFAULT 1000,
      features TEXT, -- JSON com funcionalidades
      stripe_price_id TEXT,
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Tabela de empresas/clientes
  db.exec(`
    CREATE TABLE IF NOT EXISTS companies (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      domain TEXT UNIQUE,
      email TEXT NOT NULL,
      phone TEXT,
      plan_id INTEGER NOT NULL,
      status TEXT DEFAULT 'trial', -- trial, active, suspended, expired
      trial_ends_at DATETIME,
      subscription_ends_at DATETIME,
      leads_used_current_month INTEGER DEFAULT 0,
      campaigns_active INTEGER DEFAULT 0,
      whitelabel_config TEXT, -- JSON com configurações de personalização
      stripe_customer_id TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (plan_id) REFERENCES plans (id)
    )
  `);

  // Tabela de transações e pagamentos
  db.exec(`
    CREATE TABLE IF NOT EXISTS transactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      company_id INTEGER NOT NULL,
      plan_id INTEGER NOT NULL,
      amount DECIMAL(10,2) NOT NULL,
      currency TEXT DEFAULT 'BRL',
      payment_method TEXT NOT NULL, -- stripe, pix
      stripe_payment_intent_id TEXT,
      pix_key TEXT DEFAULT '53882441000120',
      pix_qr_code TEXT,
      pix_receipt_url TEXT,
      status TEXT DEFAULT 'pending', -- pending, paid, failed, cancelled
      paid_at DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (company_id) REFERENCES companies (id),
      FOREIGN KEY (plan_id) REFERENCES plans (id)
    )
  `);

  // Tabela de configurações WhiteLabel por empresa
  db.exec(`
    CREATE TABLE IF NOT EXISTS company_customizations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      company_id INTEGER NOT NULL,
      logo_url TEXT,
      favicon_url TEXT,
      primary_color TEXT DEFAULT '#1e40af',
      secondary_color TEXT DEFAULT '#f59e0b',
      background_color TEXT DEFAULT '#ffffff',
      text_color TEXT DEFAULT '#1f2937',
      font_family TEXT DEFAULT 'Inter',
      custom_css TEXT,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (company_id) REFERENCES companies (id),
      UNIQUE(company_id)
    )
  `);

  console.log('Tabelas criadas com sucesso!');
};

// Criar planos padrão do CaptaFlow
const createDefaultPlans = () => {
  const existingPlans = db.prepare('SELECT COUNT(*) as count FROM plans').get();
  
  if (existingPlans.count === 0) {
    const stmt = db.prepare(`
      INSERT INTO plans (name, price, max_campaigns, max_leads_per_month, features)
      VALUES (?, ?, ?, ?, ?)
    `);

    const plans = [
      {
        name: 'Essencial',
        price: 149.00,
        max_campaigns: 1,
        max_leads_per_month: 1000,
        features: JSON.stringify([
          '1 campanha ativa',
          'Até 1.000 leads/mês',
          'Painel administrativo',
          'Relatórios básicos',
          'Suporte por email'
        ])
      },
      {
        name: 'Growth',
        price: 299.00,
        max_campaigns: 3,
        max_leads_per_month: 5000,
        features: JSON.stringify([
          '3 campanhas ativas',
          'Até 5.000 leads/mês',
          'Domínio personalizado',
          'Relatórios avançados',
          'Integrações WhatsApp/CRM',
          'Suporte prioritário'
        ])
      },
      {
        name: 'Agency',
        price: 699.00,
        max_campaigns: -1, // ilimitado
        max_leads_per_month: -1, // ilimitado
        features: JSON.stringify([
          'Campanhas ilimitadas',
          'Leads ilimitados',
          'White-label disponível',
          'API completa',
          'Múltiplos usuários',
          'Gerente dedicado'
        ])
      }
    ];

    plans.forEach(plan => {
      stmt.run(plan.name, plan.price, plan.max_campaigns, plan.max_leads_per_month, plan.features);
    });

    console.log('Planos padrão do CaptaFlow criados!');
  }
};

// Criar usuário super admin padrão
const createSuperAdminUser = async () => {
  const existingUser = db.prepare('SELECT * FROM users WHERE username = ?').get('captaflow_admin');
  
  if (!existingUser) {
    const hashedPassword = await bcrypt.hash('captaflow2024!', 10);
    const stmt = db.prepare(`
      INSERT INTO users (username, password, email, role, company_id)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    stmt.run('captaflow_admin', hashedPassword, '<EMAIL>', 'super_admin', null);
    console.log('Usuário Super Admin criado - Username: captaflow_admin, Password: captaflow2024!');
  }
};

// Inserir dados iniciais
const insertDefaultData = () => {
  // Verificar se já existem dados para evitar duplicação
  const existingGallery = db.prepare('SELECT COUNT(*) as count FROM gallery_images').get();
  const existingVideos = db.prepare('SELECT COUNT(*) as count FROM videos').get();
  const existingHeroContent = db.prepare('SELECT COUNT(*) as count FROM content_management WHERE section = "hero"').get();
  
  // Se já existem dados, não inserir novamente
  if (existingGallery.count > 0 || existingVideos.count > 0 || existingHeroContent.count > 0) {
    console.log('Dados iniciais já existem, pulando inserção...');
    return;
  }

  // Inserir configurações padrão do site
  const settingsStmt = db.prepare(`
    INSERT OR IGNORE INTO site_settings (key, value, description)
    VALUES (?, ?, ?)
  `);

  const defaultSettings = [
    ['site_title', 'Sollara Garden', 'Título do site'],
    ['site_description', 'Empreendimento imobiliário de luxo', 'Descrição do site'],
    ['contact_email', '<EMAIL>', 'Email de contato'],
    ['contact_phone', '(11) 99999-9999', 'Telefone de contato'],
    ['hero_video_url', '', 'URL do vídeo principal'],
    ['whatsapp_number', '5511999999999', 'Número do WhatsApp']
  ];

  defaultSettings.forEach(([key, value, description]) => {
    settingsStmt.run(key, value, description);
  });

  // Popular dados da galeria
  const galleryImages = [
    {
      title: 'Vista Aérea',
      description: 'Condomínio com área de lazer completa',
      image_url: '/lovable-uploads/076d0f9c-cbe0-478a-a8e0-82a18c4be423.png',
      order_index: 1
    },
    {
      title: 'Fachadas Modernas',
      description: 'Design contemporâneo e elegante',
      image_url: '/lovable-uploads/daec1aa2-95fd-4ad7-ad79-83d3e641f7f2.png',
      order_index: 2
    },
    {
      title: 'Portaria Elegante',
      description: 'Entrada com segurança e sofisticação',
      image_url: '/lovable-uploads/40772c9a-33d3-43d2-bc8a-b4cd636f28ae.png',
      order_index: 3
    },
    {
      title: 'Área Esportiva',
      description: 'Quadras e piscina para toda família',
      image_url: '/lovable-uploads/d912767b-33c2-4fda-bc3a-e1a7d4c736f8.png',
      order_index: 4
    },
    {
      title: 'Residências Modernas',
      description: 'Casas com garagem e acabamento premium',
      image_url: '/lovable-uploads/6f379a2e-244d-4691-8766-fc92f3f7e0ad.png',
      order_index: 5
    }
  ];

  const galleryStmt = db.prepare(`
    INSERT OR IGNORE INTO gallery_images (title, description, image_url, order_index, is_active)
    VALUES (?, ?, ?, ?, 1)
  `);

  galleryImages.forEach(image => {
    galleryStmt.run(image.title, image.description, image.image_url, image.order_index);
  });

  // Popular vídeo principal
  const videoStmt = db.prepare(`
    INSERT OR IGNORE INTO videos (title, description, url, category, position, active)
    VALUES (?, ?, ?, ?, ?, ?)
  `);

  videoStmt.run(
    'SOLLARA GARDEN - Vídeo Principal',
    'Novidade na região Sul Fluminense',
    'https://youtube.com/shorts/5yVlGgId68A?si=OtBBLC3zousFbFgs',
    'hero',
    1,
    1
  );

  // Popular dados do hero
  const heroStmt = db.prepare(`
    INSERT OR REPLACE INTO content_management (section, type, content, position, active)
    VALUES (?, ?, ?, 0, 1)
  `);

  const heroData = [
    { type: 'title', content: 'SOLLARA GARDEN' },
    { type: 'subtitle', content: 'Seu novo lar em Barra Mansa' },
    { type: 'description', content: 'Um empreendimento exclusivo que combina luxo, conforto e localização privilegiada no coração do Vale do Paraíba.' },
    { type: 'video_url', content: 'https://youtube.com/shorts/5yVlGgId68A?si=OtBBLC3zousFbFgs' },
    { type: 'video_type', content: 'youtube' },
    { type: 'background_image', content: '/lovable-uploads/076d0f9c-cbe0-478a-a8e0-82a18c4be423.png' }
  ];

  heroData.forEach(item => {
    heroStmt.run('hero', item.type, item.content);
  });

  // Popular dados do rodapé
  const footerStmt = db.prepare(`
    INSERT OR REPLACE INTO content_management (section, type, content, position, active)
    VALUES (?, ?, ?, 0, 1)
  `);

  const footerData = [
    { type: 'company_name', content: 'SOLLARA GARDEN BARRA MANSA' },
    { type: 'tagline', content: 'Grupo Salha Empreendimentos' },
    { type: 'description', content: 'Transformando sonhos em realidade há mais de 30 anos na região do Vale do Paraíba' },
    { type: 'creci', content: '00000-J' },
    { type: 'cnpj', content: '00.000.000/0001-00' }
  ];

  footerData.forEach(item => {
    footerStmt.run('footer', item.type, item.content);
  });

  console.log('Dados iniciais inseridos com sucesso!');
};

// Inicializar banco
const initDatabase = async () => {
  try {
    createTables();
    createDefaultPlans();
    await createSuperAdminUser();
    insertDefaultData();
    console.log('Banco de dados SaaS CaptaFlow inicializado com sucesso!');
  } catch (error) {
    console.error('Erro ao inicializar banco:', error);
  }
};

module.exports = { db, initDatabase }; 