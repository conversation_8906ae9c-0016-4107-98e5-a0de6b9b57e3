import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { toast } from 'sonner';
import { 
  CreditCard, 
  Download, 
  Calendar, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  ArrowRight,
  TrendingUp,
  TrendingDown,
  Crown,
  Star,
  Zap
} from 'lucide-react';

interface Plan {
  id: number;
  name: string;
  price: number;
  max_campaigns: number;
  max_leads_per_month: number;
  features: string[];
}

interface CurrentPlan {
  id: number;
  name: string;
  plan_name: string;
  plan_price: number;
  status: string;
  trial_ends_at: string;
  subscription_ends_at: string;
  days_remaining: number;
  features: string[];
}

interface Transaction {
  id: number;
  amount: number;
  status: string;
  payment_method: string;
  plan_name: string;
  paid_at: string;
  created_at: string;
}

const BillingPage: React.FC = () => {
  const [currentPlan, setCurrentPlan] = useState<CurrentPlan | null>(null);
  const [availablePlans, setAvailablePlans] = useState<Plan[]>([]);
  const [billingHistory, setBillingHistory] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [changingPlan, setChangingPlan] = useState<number | null>(null);
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [currentPlanRes, plansRes, billingRes] = await Promise.all([
        fetch('/api/company/current-plan', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        }),
        fetch('/api/plans'),
        fetch('/api/company/billing-history', {
          headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
        })
      ]);

      if (currentPlanRes.ok) {
        const currentPlanData = await currentPlanRes.json();
        setCurrentPlan(currentPlanData.data);
      }

      if (plansRes.ok) {
        const plansData = await plansRes.json();
        setAvailablePlans(plansData.data.map((plan: any) => ({
          ...plan,
          features: JSON.parse(plan.features || '[]')
        })));
      }

      if (billingRes.ok) {
        const billingData = await billingRes.json();
        setBillingHistory(billingData.data);
      }

    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      toast.error('Erro ao carregar informações de cobrança');
    } finally {
      setLoading(false);
    }
  };

  const handlePlanChange = async (newPlanId: number, paymentMethod: 'stripe' | 'pix') => {
    setChangingPlan(newPlanId);

    try {
      const response = await fetch('/api/company/change-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          new_plan_id: newPlanId,
          payment_method: paymentMethod
        })
      });

      const data = await response.json();

      if (data.success) {
        if (data.payment_required) {
          if (paymentMethod === 'stripe') {
            window.location.href = data.checkout_url;
          } else {
            navigate('/payment/pix', {
              state: {
                pixData: data.pix_data,
                planName: availablePlans.find(p => p.id === newPlanId)?.name
              }
            });
          }
        } else {
          toast.success(data.message);
          fetchData(); // Recarregar dados
        }
      } else {
        toast.error(data.error || 'Erro ao alterar plano');
      }
    } catch (error) {
      console.error('Erro ao alterar plano:', error);
      toast.error('Erro ao processar alteração de plano');
    } finally {
      setChangingPlan(null);
    }
  };

  const handleCancelSubscription = async () => {
    if (!confirm('Tem certeza que deseja cancelar sua assinatura?')) {
      return;
    }

    try {
      const response = await fetch('/api/company/cancel-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        fetchData();
      } else {
        toast.error(data.error || 'Erro ao cancelar assinatura');
      }
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      toast.error('Erro ao cancelar assinatura');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      trial: { icon: Clock, color: 'bg-blue-100 text-blue-800', text: 'Trial' },
      active: { icon: CheckCircle, color: 'bg-green-100 text-green-800', text: 'Ativo' },
      expired: { icon: XCircle, color: 'bg-red-100 text-red-800', text: 'Expirado' },
      cancelled: { icon: XCircle, color: 'bg-gray-100 text-gray-800', text: 'Cancelado' },
      suspended: { icon: AlertTriangle, color: 'bg-yellow-100 text-yellow-800', text: 'Suspenso' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.text}
      </span>
    );
  };

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'essencial':
        return <Star className="h-6 w-6 text-blue-500" />;
      case 'growth':
        return <Zap className="h-6 w-6 text-green-500" />;
      case 'agency':
        return <Crown className="h-6 w-6 text-purple-500" />;
      default:
        return <Star className="h-6 w-6 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando informações de cobrança...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Cobrança e Assinatura
          </h1>
          <p className="text-gray-600">
            Gerencie seu plano, visualize faturas e controle sua assinatura.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Plano Atual */}
          <div className="lg:col-span-2 space-y-8">
            {/* Informações do Plano Atual */}
            {currentPlan && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-900">Plano Atual</h2>
                  {getStatusBadge(currentPlan.status)}
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {getPlanIcon(currentPlan.plan_name)}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">
                      {currentPlan.plan_name}
                    </h3>
                    <p className="text-2xl font-bold text-gray-900 mb-2">
                      {formatCurrency(currentPlan.plan_price)}/mês
                    </p>
                    
                    {/* Dias restantes */}
                    {currentPlan.days_remaining !== null && (
                      <div className="mb-4">
                        {currentPlan.status === 'trial' ? (
                          <p className="text-sm text-blue-600">
                            <Clock className="h-4 w-4 inline mr-1" />
                            {currentPlan.days_remaining} dias restantes no trial
                          </p>
                        ) : (
                          <p className="text-sm text-gray-600">
                            <Calendar className="h-4 w-4 inline mr-1" />
                            Renovação em {currentPlan.days_remaining} dias
                          </p>
                        )}
                      </div>
                    )}

                    {/* Features */}
                    <div className="space-y-2">
                      {currentPlan.features.slice(0, 3).map((feature, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                          {feature}
                        </div>
                      ))}
                      {currentPlan.features.length > 3 && (
                        <p className="text-sm text-gray-500">
                          +{currentPlan.features.length - 3} funcionalidades adicionais
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Ações */}
                <div className="mt-6 flex space-x-3">
                  <button
                    onClick={() => navigate('/plans')}
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Alterar Plano
                  </button>
                  {currentPlan.status === 'active' && (
                    <button
                      onClick={handleCancelSubscription}
                      className="px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors"
                    >
                      Cancelar
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Histórico de Faturas */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">Histórico de Faturas</h2>
              </div>
              
              <div className="divide-y divide-gray-200">
                {billingHistory.length === 0 ? (
                  <div className="p-6 text-center text-gray-500">
                    Nenhuma fatura encontrada
                  </div>
                ) : (
                  billingHistory.map((transaction) => (
                    <div key={transaction.id} className="p-6 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            <CreditCard className="h-8 w-8 text-gray-400" />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900">
                              {transaction.plan_name}
                            </h3>
                            <p className="text-sm text-gray-600">
                              {formatDate(transaction.paid_at)} • {transaction.payment_method.toUpperCase()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">
                            {formatCurrency(transaction.amount)}
                          </p>
                          <div className="flex items-center space-x-2">
                            {getStatusBadge(transaction.status)}
                            <button className="text-blue-600 hover:text-blue-700">
                              <Download className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Sidebar - Planos Disponíveis */}
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">
                Planos Disponíveis
              </h3>
              
              <div className="space-y-4">
                {availablePlans.map((plan) => {
                  const isCurrentPlan = currentPlan?.plan_name === plan.name;
                  const isUpgrade = currentPlan && plan.price > currentPlan.plan_price;
                  
                  return (
                    <div
                      key={plan.id}
                      className={`p-4 rounded-lg border ${
                        isCurrentPlan 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getPlanIcon(plan.name)}
                          <h4 className="font-semibold text-gray-900">
                            {plan.name}
                          </h4>
                        </div>
                        {isUpgrade && (
                          <TrendingUp className="h-4 w-4 text-green-500" />
                        )}
                        {!isUpgrade && !isCurrentPlan && currentPlan && (
                          <TrendingDown className="h-4 w-4 text-orange-500" />
                        )}
                      </div>
                      
                      <p className="text-lg font-bold text-gray-900 mb-2">
                        {formatCurrency(plan.price)}/mês
                      </p>
                      
                      <div className="text-xs text-gray-600 mb-3">
                        {plan.max_campaigns === -1 ? 'Ilimitado' : `${plan.max_campaigns} campanhas`}
                        {' • '}
                        {plan.max_leads_per_month === -1 ? 'Leads ilimitados' : `${plan.max_leads_per_month} leads`}
                      </div>

                      {!isCurrentPlan && (
                        <div className="space-y-2">
                          <button
                            onClick={() => handlePlanChange(plan.id, 'stripe')}
                            disabled={changingPlan === plan.id}
                            className="w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                          >
                            {changingPlan === plan.id ? 'Processando...' : 'Cartão'}
                          </button>
                          <button
                            onClick={() => handlePlanChange(plan.id, 'pix')}
                            disabled={changingPlan === plan.id}
                            className="w-full border border-gray-300 text-gray-700 py-2 px-3 rounded text-sm hover:bg-gray-50 disabled:opacity-50"
                          >
                            PIX
                          </button>
                        </div>
                      )}
                      
                      {isCurrentPlan && (
                        <div className="text-center py-2">
                          <span className="text-sm font-medium text-blue-600">
                            Plano Atual
                          </span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Informações de Suporte */}
            <div className="bg-blue-50 rounded-xl border border-blue-200 p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                Precisa de Ajuda?
              </h3>
              <p className="text-blue-800 text-sm mb-4">
                Nossa equipe está pronta para ajudar com questões de cobrança e planos.
              </p>
              <div className="space-y-2 text-sm text-blue-800">
                <p>📱 WhatsApp: (21) 98230-1476</p>
                <p>📧 Email: <EMAIL></p>
                <p>⏰ Seg-Sex: 9h às 18h</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingPage; 