import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAutoTheme } from '@/hooks/useTheme';

interface ThemeContextType {
  theme: any;
  loading: boolean;
  error: string | null;
  domain: string;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useThemeContext = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { theme, loading, error, domain } = useAutoTheme();

  return (
    <ThemeContext.Provider value={{ theme, loading, error, domain }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Componente para aplicar estilos CSS customizados
export const CustomStyles: React.FC = () => {
  const { theme } = useThemeContext();

  useEffect(() => {
    // Aplicar CSS customizado se disponível
    if (theme?.custom_css) {
      let customStyle = document.getElementById('custom-theme-css') as HTMLStyleElement;
      if (!customStyle) {
        customStyle = document.createElement('style');
        customStyle.id = 'custom-theme-css';
        document.head.appendChild(customStyle);
      }
      customStyle.textContent = theme.custom_css;
    }

    // Aplicar favicon se disponível
    if (theme?.favicon_url) {
      let favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
      if (!favicon) {
        favicon = document.createElement('link');
        favicon.rel = 'icon';
        document.head.appendChild(favicon);
      }
      favicon.href = theme.favicon_url;
    }

    // Aplicar fonte do Google Fonts se necessário
    if (theme?.font_family && theme.font_family !== 'Inter') {
      let fontLink = document.getElementById('google-fonts') as HTMLLinkElement;
      if (!fontLink) {
        fontLink = document.createElement('link');
        fontLink.id = 'google-fonts';
        fontLink.rel = 'stylesheet';
        document.head.appendChild(fontLink);
      }
      fontLink.href = `https://fonts.googleapis.com/css2?family=${theme.font_family.replace(' ', '+')}:wght@300;400;500;600;700&display=swap`;
    }
  }, [theme]);

  return null;
};

// Componente para logo dinâmico
interface DynamicLogoProps {
  className?: string;
  fallbackText?: string;
}

export const DynamicLogo: React.FC<DynamicLogoProps> = ({ 
  className = "h-8 object-contain", 
  fallbackText = "Sua Marca" 
}) => {
  const { theme } = useThemeContext();

  if (theme?.logo_url) {
    return (
      <img 
        src={theme.logo_url} 
        alt="Logo" 
        className={className}
        onError={(e) => {
          // Fallback em caso de erro no carregamento da imagem
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          const fallback = target.nextElementSibling as HTMLElement;
          if (fallback) {
            fallback.style.display = 'block';
          }
        }}
      />
    );
  }

  return (
    <div 
      className={`px-4 py-2 rounded font-bold text-white ${className}`}
      style={{ backgroundColor: theme?.primary_color || '#1e40af' }}
    >
      {fallbackText}
    </div>
  );
};

// Componente para botões com cores do tema
interface ThemedButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  className?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
}

export const ThemedButton: React.FC<ThemedButtonProps> = ({ 
  children, 
  variant = 'primary', 
  className = '', 
  onClick,
  type = 'button',
  disabled = false
}) => {
  const { theme } = useThemeContext();

  const getButtonStyles = () => {
    const baseStyles = 'px-6 py-3 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed';
    
    if (variant === 'primary') {
      return {
        backgroundColor: theme?.primary_color || '#1e40af',
        color: '#ffffff',
        className: `${baseStyles} hover:opacity-90 active:scale-95 ${className}`
      };
    } else {
      return {
        backgroundColor: theme?.secondary_color || '#f59e0b',
        color: '#ffffff',
        className: `${baseStyles} hover:opacity-90 active:scale-95 ${className}`
      };
    }
  };

  const styles = getButtonStyles();

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={styles.className}
      style={{
        backgroundColor: styles.backgroundColor,
        color: styles.color
      }}
    >
      {children}
    </button>
  );
};

// Componente para texto com cor do tema
interface ThemedTextProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'text';
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
}

export const ThemedText: React.FC<ThemedTextProps> = ({ 
  children, 
  variant = 'text', 
  className = '',
  as: Component = 'p'
}) => {
  const { theme } = useThemeContext();

  const getTextColor = () => {
    switch (variant) {
      case 'primary':
        return theme?.primary_color || '#1e40af';
      case 'secondary':
        return theme?.secondary_color || '#f59e0b';
      default:
        return theme?.text_color || '#1f2937';
    }
  };

  return (
    <Component
      className={className}
      style={{
        color: getTextColor(),
        fontFamily: theme?.font_family || 'Inter'
      }}
    >
      {children}
    </Component>
  );
};

// Componente para container com cor de fundo do tema
interface ThemedContainerProps {
  children: React.ReactNode;
  className?: string;
}

export const ThemedContainer: React.FC<ThemedContainerProps> = ({ 
  children, 
  className = '' 
}) => {
  const { theme } = useThemeContext();

  return (
    <div
      className={className}
      style={{
        backgroundColor: theme?.background_color || '#ffffff',
        color: theme?.text_color || '#1f2937',
        fontFamily: theme?.font_family || 'Inter'
      }}
    >
      {children}
    </div>
  );
};
