import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { toast } from 'sonner';

interface Plan {
  id: number;
  name: string;
  price: number;
  max_campaigns: number;
  max_leads_per_month: number;
  features: string[];
}

const CompanyRegistration = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [plans, setPlans] = useState<Plan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [domainAvailable, setDomainAvailable] = useState<boolean | null>(null);
  const [checkingDomain, setCheckingDomain] = useState(false);

  const [formData, setFormData] = useState({
    companyName: '',
    domain: '',
    email: '',
    phone: '',
    adminName: '',
    adminPassword: '',
    confirmPassword: ''
  });

  // Carregar planos disponíveis
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        const response = await fetch('/api/plans');
        if (response.ok) {
          const data = await response.json();
          setPlans(data);
        }
      } catch (error) {
        console.error('Erro ao carregar planos:', error);
      }
    };
    fetchPlans();
  }, []);

  // Verificar disponibilidade do domínio
  const checkDomainAvailability = async (domain: string) => {
    if (!domain || domain.length < 3) {
      setDomainAvailable(null);
      return;
    }

    setCheckingDomain(true);
    try {
      const response = await fetch(`/api/check-domain?domain=${encodeURIComponent(domain)}`);
      const data = await response.json();
      setDomainAvailable(data.available);
    } catch (error) {
      console.error('Erro ao verificar domínio:', error);
      setDomainAvailable(null);
    } finally {
      setCheckingDomain(false);
    }
  };

  // Handle form changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Verificar domínio automaticamente
    if (field === 'domain') {
      const cleanDomain = value.toLowerCase().replace(/[^a-z0-9-]/g, '');
      setFormData(prev => ({
        ...prev,
        domain: cleanDomain
      }));
      
      const timer = setTimeout(() => {
        checkDomainAvailability(cleanDomain);
      }, 500);
      
      return () => clearTimeout(timer);
    }
  };

  // Validar formulário
  const validateForm = () => {
    if (!formData.companyName || formData.companyName.length < 2) {
      toast.error('Nome da empresa deve ter pelo menos 2 caracteres');
      return false;
    }

    if (!formData.domain || formData.domain.length < 3) {
      toast.error('Domínio deve ter pelo menos 3 caracteres');
      return false;
    }

    if (domainAvailable === false) {
      toast.error('Este domínio não está disponível');
      return false;
    }

    if (!formData.email || !formData.email.includes('@')) {
      toast.error('Email inválido');
      return false;
    }

    if (!formData.adminName || formData.adminName.length < 2) {
      toast.error('Nome do administrador é obrigatório');
      return false;
    }

    if (!formData.adminPassword || formData.adminPassword.length < 6) {
      toast.error('Senha deve ter pelo menos 6 caracteres');
      return false;
    }

    if (formData.adminPassword !== formData.confirmPassword) {
      toast.error('Senhas não conferem');
      return false;
    }

    if (!selectedPlan) {
      toast.error('Selecione um plano');
      return false;
    }

    return true;
  };

  // Submeter formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    try {
      const response = await fetch('/api/company/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          planId: selectedPlan
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Empresa cadastrada com sucesso!');
        toast.info('Verifique seu email para ativar a conta');
        navigate('/login');
      } else {
        toast.error(data.error || 'Erro ao cadastrar empresa');
      }
    } catch (error) {
      console.error('Erro ao cadastrar:', error);
      toast.error('Erro interno do servidor');
    } finally {
      setLoading(false);
    }
  };

  const selectedPlanData = plans.find(p => p.id.toString() === selectedPlan);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        <Card className="shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold text-gray-900">
              Cadastre sua Empresa
            </CardTitle>
            <CardDescription className="text-lg">
              Comece seu trial gratuito de 14 dias no CaptaFlow
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Informações da Empresa */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Informações da Empresa</h3>
                  
                  <div>
                    <Label htmlFor="companyName">Nome da Empresa *</Label>
                    <Input
                      id="companyName"
                      value={formData.companyName}
                      onChange={(e) => handleInputChange('companyName', e.target.value)}
                      placeholder="Sua Empresa Ltda"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="domain">Domínio Personalizado *</Label>
                    <div className="relative">
                      <Input
                        id="domain"
                        value={formData.domain}
                        onChange={(e) => handleInputChange('domain', e.target.value)}
                        placeholder="minhaempresa"
                        required
                        className="pr-10"
                      />
                      <div className="absolute right-3 top-3">
                        {checkingDomain && <Loader2 className="h-4 w-4 animate-spin" />}
                        {!checkingDomain && domainAvailable === true && <CheckCircle className="h-4 w-4 text-green-500" />}
                        {!checkingDomain && domainAvailable === false && <XCircle className="h-4 w-4 text-red-500" />}
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Seu site ficará: {formData.domain}.captaflow.com
                    </p>
                    {domainAvailable === false && (
                      <Alert className="mt-2">
                        <AlertDescription className="text-red-600">
                          Este domínio não está disponível. Tente outro.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="email">Email da Empresa *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="phone">Telefone</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="(11) 99999-9999"
                    />
                  </div>
                </div>

                {/* Informações do Administrador */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Administrador Principal</h3>
                  
                  <div>
                    <Label htmlFor="adminName">Nome Completo *</Label>
                    <Input
                      id="adminName"
                      value={formData.adminName}
                      onChange={(e) => handleInputChange('adminName', e.target.value)}
                      placeholder="João Silva"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="adminPassword">Senha *</Label>
                    <Input
                      id="adminPassword"
                      type="password"
                      value={formData.adminPassword}
                      onChange={(e) => handleInputChange('adminPassword', e.target.value)}
                      placeholder="Mínimo 6 caracteres"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="confirmPassword">Confirmar Senha *</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      placeholder="Confirme sua senha"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Seleção de Plano */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Escolha seu Plano</h3>
                <Select value={selectedPlan} onValueChange={setSelectedPlan}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um plano" />
                  </SelectTrigger>
                  <SelectContent>
                    {plans.map((plan) => (
                      <SelectItem key={plan.id} value={plan.id.toString()}>
                        {plan.name} - R$ {plan.price.toFixed(2)}/mês
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {selectedPlanData && (
                  <Card className="bg-blue-50 border-blue-200">
                    <CardContent className="pt-4">
                      <h4 className="font-semibold text-blue-900">{selectedPlanData.name}</h4>
                      <p className="text-2xl font-bold text-blue-900 mb-2">
                        R$ {selectedPlanData.price.toFixed(2)}<span className="text-sm font-normal">/mês</span>
                      </p>
                      <ul className="text-sm text-blue-800 space-y-1">
                        {selectedPlanData.features.map((feature, index) => (
                          <li key={index} className="flex items-center">
                            <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                      <Alert className="mt-3 bg-green-50 border-green-200">
                        <AlertDescription className="text-green-800">
                          ✨ <strong>Trial gratuito de 14 dias!</strong> Cancele a qualquer momento.
                        </AlertDescription>
                      </Alert>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Botões */}
              <div className="flex justify-between items-center pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/')}
                >
                  Voltar
                </Button>
                
                <Button
                  type="submit"
                  disabled={loading || domainAvailable === false}
                  className="min-w-32"
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Cadastrando...
                    </>
                  ) : (
                    'Iniciar Trial Gratuito'
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CompanyRegistration; 