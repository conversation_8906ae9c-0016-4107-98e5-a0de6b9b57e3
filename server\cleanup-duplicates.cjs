const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, 'database.sqlite');
const db = new Database(dbPath);

console.log('🧹 Iniciando limpeza de dados duplicados...');

// Limpar imagens duplicadas na galeria
const cleanupGalleryDuplicates = () => {
  console.log('📸 Limpando imagens duplicadas da galeria...');
  
  // Manter apenas o primeiro registro de cada image_url
  const duplicateImages = db.prepare(`
    DELETE FROM gallery_images 
    WHERE id NOT IN (
      SELECT MIN(id) 
      FROM gallery_images 
      GROUP BY image_url
    )
  `).run();
  
  console.log(`   ✅ ${duplicateImages.changes} imagens duplicadas removidas`);
};

// Limpar vídeos duplicados
const cleanupVideoDuplicates = () => {
  console.log('🎥 Limpando vídeos duplicados...');
  
  // Manter apenas o primeiro registro de cada url
  const duplicateVideos = db.prepare(`
    DELETE FROM videos 
    WHERE id NOT IN (
      SELECT MIN(id) 
      FROM videos 
      GROUP BY url
    )
  `).run();
  
  console.log(`   ✅ ${duplicateVideos.changes} vídeos duplicados removidos`);
};

// Limpar conteúdo duplicado
const cleanupContentDuplicates = () => {
  console.log('📝 Limpando conteúdo duplicado...');
  
  // Manter apenas o primeiro registro de cada section+type
  const duplicateContent = db.prepare(`
    DELETE FROM content_management 
    WHERE id NOT IN (
      SELECT MIN(id) 
      FROM content_management 
      GROUP BY section, type
    )
  `).run();
  
  console.log(`   ✅ ${duplicateContent.changes} conteúdos duplicados removidos`);
};

// Reordenar índices da galeria
const reorderGalleryImages = () => {
  console.log('🔢 Reordenando índices da galeria...');
  
  const images = db.prepare('SELECT id FROM gallery_images ORDER BY id').all();
  const updateStmt = db.prepare('UPDATE gallery_images SET order_index = ? WHERE id = ?');
  
  images.forEach((image, index) => {
    updateStmt.run(index + 1, image.id);
  });
  
  console.log(`   ✅ ${images.length} imagens reordenadas`);
};

// Executar limpeza
try {
  cleanupGalleryDuplicates();
  cleanupVideoDuplicates();
  cleanupContentDuplicates();
  reorderGalleryImages();
  
  console.log('✨ Limpeza concluída com sucesso!');
  
  // Mostrar estatísticas finais
  const galleryCount = db.prepare('SELECT COUNT(*) as count FROM gallery_images').get();
  const videoCount = db.prepare('SELECT COUNT(*) as count FROM videos').get();
  const contentCount = db.prepare('SELECT COUNT(*) as count FROM content_management').get();
  
  console.log('\n📊 Estatísticas atuais:');
  console.log(`   📸 Imagens da galeria: ${galleryCount.count}`);
  console.log(`   🎥 Vídeos: ${videoCount.count}`);
  console.log(`   📝 Conteúdos: ${contentCount.count}`);
  
} catch (error) {
  console.error('❌ Erro durante a limpeza:', error);
} finally {
  db.close();
} 