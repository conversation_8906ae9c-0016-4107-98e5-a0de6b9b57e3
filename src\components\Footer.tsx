
import React, { useState, useEffect } from 'react';
import { ApiService } from '@/lib/api';

interface FooterData {
  logo_url?: string;
  company_name?: string;
  tagline?: string;
  description?: string;
  creci?: string;
  cnpj?: string;
  copyright_text?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  social_facebook?: string;
  social_instagram?: string;
  social_youtube?: string;
  social_linkedin?: string;
}

const Footer = () => {
  const [footerData, setFooterData] = useState<FooterData>({
    logo_url: '/lovable-uploads/51ca6b6b-95b1-4314-bad6-7305c41b418e.png',
    company_name: 'SOLLARA GARDEN BARRA MANSA',
    tagline: 'Grupo Salha Empreendimentos',
    description: 'Transformando sonhos em realidade há mais de 30 anos na região do Vale do Paraíba',
    copyright_text: `© ${new Date().getFullYear()} Sollara Garden. Todos os direitos reservados.`
  });

  useEffect(() => {
    const loadFooterData = async () => {
      try {
        const response = await ApiService.getPublicFooterContent();
        if ((response as any).success && (response as any).data) {
          setFooterData(prev => ({
            ...prev,
            ...(response as any).data
          }));
        }
      } catch (error) {
        console.error('Erro ao carregar dados do rodapé:', error);
        // Usar dados padrão em caso de erro
      }
    };

    loadFooterData();
  }, []);

  return (
    <footer className="bg-luxury-brown-dark text-white py-12">
      <div className="container mx-auto px-4 md:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            {/* Logo */}
            {footerData.logo_url && (
            <div className="mb-8">
              <img 
                  src={footerData.logo_url} 
                  alt="Logo"
                className="mx-auto h-32 md:h-40 w-auto"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/lovable-uploads/51ca6b6b-95b1-4314-bad6-7305c41b418e.png';
                  }}
              />
            </div>
            )}
            
            {/* Nome e Descrição da Empresa */}
            {footerData.company_name && (
            <div className="mb-6">
              <p className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 text-lg font-semibold mb-2">
                  {footerData.company_name}
                </p>
                {footerData.tagline && (
                  <p className="text-luxury-gold text-base font-medium mb-2">
                    {footerData.tagline}
                  </p>
                )}
                {footerData.description && (
                  <p className="text-white/80 text-sm max-w-2xl mx-auto">
                    {footerData.description}
                  </p>
                )}
              </div>
            )}

            {/* Informações de Contato e Legais */}
            {(footerData.contact_email || footerData.contact_phone || footerData.address || footerData.creci || footerData.cnpj) && (
              <div className="mb-6 text-sm text-white/70 space-y-1">
                {footerData.contact_email && (
                  <p>
                    <a 
                      href={`mailto:${footerData.contact_email}`}
                      className="hover:text-luxury-gold transition-colors"
                    >
                      Email: {footerData.contact_email}
                    </a>
                  </p>
                )}
                {footerData.contact_phone && (
                  <p>
                    <a 
                      href={`tel:${footerData.contact_phone}`}
                      className="hover:text-luxury-gold transition-colors"
                    >
                      Telefone: {footerData.contact_phone}
                    </a>
                  </p>
                )}
                {footerData.address && (
                  <p>{footerData.address}</p>
                )}
                {footerData.creci && (
                  <p>CRECI: {footerData.creci}</p>
                )}
                {footerData.cnpj && (
                  <p>CNPJ: {footerData.cnpj}</p>
                )}
              </div>
            )}

            {/* Redes Sociais */}
            {(footerData.social_facebook || footerData.social_instagram || footerData.social_youtube || footerData.social_linkedin) && (
              <div className="mb-6">
                <div className="flex justify-center space-x-4">
                  {footerData.social_facebook && (
                    <a 
                      href={footerData.social_facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white/70 hover:text-luxury-gold transition-colors"
                      aria-label="Facebook"
                    >
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    </a>
                  )}
                  {footerData.social_instagram && (
                    <a 
                      href={footerData.social_instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white/70 hover:text-luxury-gold transition-colors"
                      aria-label="Instagram"
                    >
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.328-1.297C4.243 14.794 3.753 13.643 3.753 12.346s.49-2.448 1.368-3.328c.88-.88 2.031-1.297 3.328-1.297s2.448.417 3.328 1.297c.88.88 1.297 2.031 1.297 3.328s-.417 2.448-1.297 3.328c-.88.807-2.031 1.297-3.328 1.297zm7.83 0c-1.297 0-2.448-.49-3.328-1.297-.88-.88-1.297-2.031-1.297-3.328s.417-2.448 1.297-3.328c.88-.88 2.031-1.297 3.328-1.297s2.448.417 3.328 1.297c.88.88 1.297 2.031 1.297 3.328s-.417 2.448-1.297 3.328c-.88.807-2.031 1.297-3.328 1.297z"/>
                      </svg>
                    </a>
                  )}
                  {footerData.social_youtube && (
                    <a 
                      href={footerData.social_youtube}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white/70 hover:text-luxury-gold transition-colors"
                      aria-label="YouTube"
                    >
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                      </svg>
                    </a>
                  )}
                  {footerData.social_linkedin && (
                    <a 
                      href={footerData.social_linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white/70 hover:text-luxury-gold transition-colors"
                      aria-label="LinkedIn"
                    >
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </a>
                  )}
                </div>
            </div>
            )}
            
            {/* Copyright */}
            <div className="border-t border-white/20 pt-8">
              <p className="text-white/60 text-sm">
                {footerData.copyright_text || `© ${new Date().getFullYear()} Sollara Garden. Todos os direitos reservados.`}
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
