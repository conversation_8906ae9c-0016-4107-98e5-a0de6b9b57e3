# Configuração do Domínio sollara-garden.gruposalha.com.br

## ⚠️ PROBLEMA ATUAL: SSL/HTTPS REDIRECT

O nginx está forçando redirecionamento para HTTPS (porta 443) mas não há certificado SSL configurado, causando erro de "batendo no SSL default".

## SOLUÇÕES DISPONÍVEIS:

### Opção 1: REMOVER HTTPS (Solução Imediata)
Use o arquivo `nginx-sollara-domain-http-only.conf` para trabalhar apenas com HTTP.

### Opção 2: CONFIGURAR SSL PROPER
Use o arquivo `nginx-sollara-domain-ssl.conf` e configure Let's Encrypt.

---

## COMANDOS PARA APLICAR AS SOLUÇÕES:

### Para usar APENAS HTTP (sem SSL):

```bash
# Copiar a configuração HTTP-only
sudo cp nginx-sollara-domain-http-only.conf /etc/nginx/sites-available/sollara-garden.conf

# Testar configuração
sudo nginx -t

# Recarregar nginx
sudo systemctl reload nginx
```

### Para configurar SSL com Let's Encrypt:

```bash
# 1. Primeiro use a configuração HTTP-only temporariamente
sudo cp nginx-sollara-domain-http-only.conf /etc/nginx/sites-available/sollara-garden.conf
sudo nginx -t && sudo systemctl reload nginx

# 2. Instalar certbot se não estiver instalado
sudo apt update
sudo apt install certbot python3-certbot-nginx

# 3. Gerar certificado SSL
sudo certbot --nginx -d sollara-garden.gruposalha.com.br

# 4. Após sucesso do certbot, usar a configuração SSL
sudo cp nginx-sollara-domain-ssl.conf /etc/nginx/sites-available/sollara-garden.conf
sudo nginx -t && sudo systemctl reload nginx
```

---

## Comandos para executar no servidor:

### 1. Atualizar a configuração do Nginx

```bash
# Editar o arquivo de configuração do sollara-garden
sudo nano /etc/nginx/sites-available/sollara-garden.conf
```

### 2. Atualizar o server_name para incluir o domínio:

Altere a linha `server_name` de:
```
server_name **************;
```

Para:
```
server_name sollara-garden.gruposalha.com.br **************;
```

### 3. Testar e recarregar o Nginx:

```bash
# Testar a configuração
sudo nginx -t

# Se passou no teste, recarregar o Nginx
sudo systemctl reload nginx
```

### 4. Reiniciar o PM2 para aplicar as mudanças de CORS:

```bash
# Reiniciar o processo do sollaragarden
pm2 restart sollaragarden

# Verificar se está funcionando
pm2 logs sollaragarden --lines 5
```

### 5. Testar o acesso:

```bash
# Testar pelo IP
curl -I http://**************

# Testar pelo domínio
curl -I http://sollara-garden.gruposalha.com.br
```

## URLs finais:

### Se usando HTTP apenas:
- **Frontend**: http://sollara-garden.gruposalha.com.br
- **API**: http://sollara-garden.gruposalha.com.br/api  
- **Painel Admin**: http://sollara-garden.gruposalha.com.br/admin

### Se usando HTTPS com SSL:
- **Frontend**: https://sollara-garden.gruposalha.com.br
- **API**: https://sollara-garden.gruposalha.com.br/api  
- **Painel Admin**: https://sollara-garden.gruposalha.com.br/admin

## Arquivos de configuração disponíveis:

1. **`nginx-sollara-domain.conf`** - Configuração com placeholder SSL (redireciona HTTPS para HTTP)
2. **`nginx-sollara-domain-http-only.conf`** - Apenas HTTP, sem SSL
3. **`nginx-sollara-domain-ssl.conf`** - Configuração completa com Let's Encrypt SSL

## Próximos passos (opcional):

Para adicionar SSL/HTTPS futuramente, use:
```bash
sudo certbot --nginx -d sollara-garden.gruposalha.com.br
``` 