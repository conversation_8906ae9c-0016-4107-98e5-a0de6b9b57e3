
import React from 'react';
import HeroSection from '@/components/HeroSection';
import VideoSection from '@/components/VideoSection';
import AboutSection from '@/components/AboutSection';
import ImageCarousel from '@/components/ImageCarousel';
import FinancingSection from '@/components/FinancingSection';
import ContactForm from '@/components/ContactForm';
import CompanySection from '@/components/CompanySection';
import Footer from '@/components/Footer';
import ScrollIndicator from '@/components/effects/ScrollIndicator';
import { ThemedContainer } from '@/components/ThemeProvider';

const Index = () => {
  return (
    <ThemedContainer className="min-h-screen">
      <ScrollIndicator />
      <HeroSection />
      <VideoSection />
      <AboutSection />
      <ImageCarousel />
      <FinancingSection />
      <ContactForm />
      <CompanySection />
      <Footer />
    </ThemedContainer>
  );
};

export default Index;
