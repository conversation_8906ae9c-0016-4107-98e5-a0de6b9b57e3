
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { ApiService } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Save, RotateCcw } from 'lucide-react';

interface FooterData {
  logo_url?: string;
  company_name?: string;
  company_description?: string;
  copyright_text?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  social_facebook?: string;
  social_instagram?: string;
  social_youtube?: string;
  social_linkedin?: string;
}

const FooterEditor = () => {
  const [footerData, setFooterData] = useState<FooterData>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadFooterData();
  }, []);

  const loadFooterData = async () => {
    try {
      const response = await ApiService.getFooterContent();
      if (response.success) {
        setFooterData(response.data || {});
      }
    } catch (error) {
      // Se não houver dados, usar valores padrão
      setFooterData({
        logo_url: '/lovable-uploads/51ca6b6b-95b1-4314-bad6-7305c41b418e.png',
        company_name: 'Grupo Salha Empreendimentos',
        company_description: 'Excelência em empreendimentos imobiliários de luxo',
        copyright_text: `© ${new Date().getFullYear()} Sollara Garden. Todos os direitos reservados.`,
        contact_email: '<EMAIL>',
        contact_phone: '(11) 99999-9999',
        address: 'São Paulo, SP'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      await ApiService.updateFooterContent(footerData);
      toast({
        title: "Sucesso",
        description: "Conteúdo do rodapé atualizado com sucesso!",
      });
    } catch (error) {
    toast({
        title: "Erro",
        description: "Falha ao salvar conteúdo do rodapé",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (confirm('Tem certeza que deseja restaurar os valores padrão?')) {
      setFooterData({
        logo_url: '/lovable-uploads/51ca6b6b-95b1-4314-bad6-7305c41b418e.png',
        company_name: 'Grupo Salha Empreendimentos',
        company_description: 'Excelência em empreendimentos imobiliários de luxo',
        copyright_text: `© ${new Date().getFullYear()} Sollara Garden. Todos os direitos reservados.`,
        contact_email: '<EMAIL>',
        contact_phone: '(11) 99999-9999',
        address: 'São Paulo, SP'
      });
    }
  };

  const updateField = (field: keyof FooterData, value: string) => {
    setFooterData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-4 border-luxury-gold border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-luxury-brown">Editor do Rodapé</h2>
          <p className="text-luxury-brown-light">Configure o conteúdo do rodapé da página principal</p>
        </div>
        
        <div className="flex space-x-2">
          <Button
            onClick={handleReset}
            variant="outline"
            className="border-luxury-gold/30 text-luxury-brown hover:bg-luxury-gold/10"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Restaurar Padrão
          </Button>
          
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-luxury-gold hover:bg-luxury-gold/90 text-luxury-brown"
          >
            <Save className="w-4 h-4 mr-2" />
            {saving ? 'Salvando...' : 'Salvar Alterações'}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Informações da Empresa */}
        <Card className="border-luxury-gold/30">
          <CardHeader>
            <CardTitle className="text-luxury-brown">Informações da Empresa</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="logo_url" className="text-luxury-brown">URL do Logo</Label>
              <Input
                id="logo_url"
                value={footerData.logo_url || ''}
                onChange={(e) => updateField('logo_url', e.target.value)}
                placeholder="https://exemplo.com/logo.png"
                className="border-luxury-gold/30 focus:border-luxury-gold"
              />
            </div>

            <div>
              <Label htmlFor="company_name" className="text-luxury-brown">Nome da Empresa</Label>
              <Input
                id="company_name"
                value={footerData.company_name || ''}
                onChange={(e) => updateField('company_name', e.target.value)}
                placeholder="Nome da empresa"
                className="border-luxury-gold/30 focus:border-luxury-gold"
              />
            </div>

            <div>
              <Label htmlFor="company_description" className="text-luxury-brown">Descrição</Label>
              <Textarea
                id="company_description"
                value={footerData.company_description || ''}
                onChange={(e) => updateField('company_description', e.target.value)}
                placeholder="Breve descrição da empresa"
                className="border-luxury-gold/30 focus:border-luxury-gold"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="copyright_text" className="text-luxury-brown">Texto de Copyright</Label>
              <Input
                id="copyright_text"
                value={footerData.copyright_text || ''}
                onChange={(e) => updateField('copyright_text', e.target.value)}
                placeholder="© 2024 Empresa. Todos os direitos reservados."
                className="border-luxury-gold/30 focus:border-luxury-gold"
              />
            </div>
          </CardContent>
        </Card>

        {/* Informações de Contato */}
        <Card className="border-luxury-gold/30">
          <CardHeader>
            <CardTitle className="text-luxury-brown">Informações de Contato</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="contact_email" className="text-luxury-brown">Email de Contato</Label>
              <Input
                id="contact_email"
                type="email"
                value={footerData.contact_email || ''}
                onChange={(e) => updateField('contact_email', e.target.value)}
                placeholder="<EMAIL>"
                className="border-luxury-gold/30 focus:border-luxury-gold"
              />
            </div>

            <div>
              <Label htmlFor="contact_phone" className="text-luxury-brown">Telefone de Contato</Label>
              <Input
                id="contact_phone"
                value={footerData.contact_phone || ''}
                onChange={(e) => updateField('contact_phone', e.target.value)}
                placeholder="(11) 99999-9999"
                className="border-luxury-gold/30 focus:border-luxury-gold"
              />
            </div>

            <div>
              <Label htmlFor="address" className="text-luxury-brown">Endereço</Label>
              <Textarea
                id="address"
                value={footerData.address || ''}
                onChange={(e) => updateField('address', e.target.value)}
                placeholder="Endereço completo da empresa"
                className="border-luxury-gold/30 focus:border-luxury-gold"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Redes Sociais */}
        <Card className="border-luxury-gold/30 md:col-span-2">
          <CardHeader>
            <CardTitle className="text-luxury-brown">Redes Sociais</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div>
                <Label htmlFor="social_facebook" className="text-luxury-brown">Facebook</Label>
                <Input
                  id="social_facebook"
                  value={footerData.social_facebook || ''}
                  onChange={(e) => updateField('social_facebook', e.target.value)}
                  placeholder="https://facebook.com/empresa"
                  className="border-luxury-gold/30 focus:border-luxury-gold"
                />
              </div>

              <div>
                <Label htmlFor="social_instagram" className="text-luxury-brown">Instagram</Label>
                <Input
                  id="social_instagram"
                  value={footerData.social_instagram || ''}
                  onChange={(e) => updateField('social_instagram', e.target.value)}
                  placeholder="https://instagram.com/empresa"
                  className="border-luxury-gold/30 focus:border-luxury-gold"
                />
              </div>

              <div>
                <Label htmlFor="social_youtube" className="text-luxury-brown">YouTube</Label>
                <Input
                  id="social_youtube"
                  value={footerData.social_youtube || ''}
                  onChange={(e) => updateField('social_youtube', e.target.value)}
                  placeholder="https://youtube.com/empresa"
                  className="border-luxury-gold/30 focus:border-luxury-gold"
                />
              </div>

              <div>
                <Label htmlFor="social_linkedin" className="text-luxury-brown">LinkedIn</Label>
                <Input
                  id="social_linkedin"
                  value={footerData.social_linkedin || ''}
                  onChange={(e) => updateField('social_linkedin', e.target.value)}
                  placeholder="https://linkedin.com/company/empresa"
                  className="border-luxury-gold/30 focus:border-luxury-gold"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Preview do Rodapé */}
      <Card className="border-luxury-gold/30">
        <CardHeader>
          <CardTitle className="text-luxury-brown">Preview do Rodapé</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-luxury-brown-dark text-white p-8 rounded-lg">
            <div className="max-w-6xl mx-auto">
              <div className="text-center">
                {/* Logo */}
                {footerData.logo_url && (
                  <div className="mb-8">
                    <img 
                      src={footerData.logo_url} 
                      alt="Logo"
                      className="mx-auto h-32 md:h-40 w-auto"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
            />
          </div>
                )}
                
                {/* Nome e Descrição da Empresa */}
                {footerData.company_name && (
                  <div className="mb-6">
                    <p className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 text-lg font-semibold mb-2">
                      {footerData.company_name}
                    </p>
                    {footerData.company_description && (
                      <p className="text-white/80 text-sm">
                        {footerData.company_description}
                      </p>
                    )}
                  </div>
                )}
                
                {/* Informações de Contato */}
                {(footerData.contact_email || footerData.contact_phone || footerData.address) && (
                  <div className="mb-6 text-sm text-white/70">
                    {footerData.contact_email && (
                      <p>Email: {footerData.contact_email}</p>
                    )}
                    {footerData.contact_phone && (
                      <p>Telefone: {footerData.contact_phone}</p>
                    )}
                    {footerData.address && (
                      <p>Endereço: {footerData.address}</p>
                    )}
                  </div>
                )}
                
                {/* Copyright */}
                <div className="border-t border-white/20 pt-8">
                  <p className="text-white/60 text-sm">
                    {footerData.copyright_text || `© ${new Date().getFullYear()} Sollara Garden. Todos os direitos reservados.`}
            </p>
          </div>
        </div>
      </div>
      </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FooterEditor;
