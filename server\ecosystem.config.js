module.exports = {
  apps: [
    {
      name: 'sollara-garden-server',
      script: 'server.cjs',
      cwd: '/opt/SollaraGarden/server',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        PORT: 3001
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      watch: false,
      max_memory_restart: '1G',
      error_file: '/root/.pm2/logs/sollara-garden-server-error.log',
      out_file: '/root/.pm2/logs/sollara-garden-server-out.log',
      log_file: '/root/.pm2/logs/sollara-garden-server-combined.log',
      time: true
    }
  ]
}; 