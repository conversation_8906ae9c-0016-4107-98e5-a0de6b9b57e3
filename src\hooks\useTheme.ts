import { useState, useEffect, useCallback } from 'react';
import { ApiService } from '@/lib/api';

interface ThemeConfig {
  id?: number;
  company_id?: number;
  logo_url?: string;
  favicon_url?: string;
  primary_color: string;
  secondary_color: string;
  background_color: string;
  text_color: string;
  font_family: string;
  custom_css?: string;
  updated_at?: string;
}

const defaultTheme: ThemeConfig = {
  primary_color: '#1e40af',
  secondary_color: '#f59e0b',
  background_color: '#ffffff',
  text_color: '#1f2937',
  font_family: 'Inter'
};

export const useTheme = () => {
  const [theme, setTheme] = useState<ThemeConfig>(defaultTheme);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Aplicar tema no DOM
  const applyTheme = useCallback((themeConfig: ThemeConfig) => {
    const root = document.documentElement;
    
    // Aplicar variáveis CSS
    root.style.setProperty('--primary-color', themeConfig.primary_color);
    root.style.setProperty('--secondary-color', themeConfig.secondary_color);
    root.style.setProperty('--background-color', themeConfig.background_color);
    root.style.setProperty('--text-color', themeConfig.text_color);
    root.style.setProperty('--font-family', themeConfig.font_family);

    // Aplicar favicon se disponível
    if (themeConfig.favicon_url) {
      let favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
      if (!favicon) {
        favicon = document.createElement('link');
        favicon.rel = 'icon';
        document.head.appendChild(favicon);
      }
      favicon.href = themeConfig.favicon_url;
    }

    // Aplicar CSS customizado
    if (themeConfig.custom_css) {
      let customStyle = document.getElementById('custom-theme-css') as HTMLStyleElement;
      if (!customStyle) {
        customStyle = document.createElement('style');
        customStyle.id = 'custom-theme-css';
        document.head.appendChild(customStyle);
      }
      customStyle.textContent = themeConfig.custom_css;
    }

    // Aplicar fonte do Google Fonts se necessário
    if (themeConfig.font_family && themeConfig.font_family !== 'Inter') {
      const fontLink = document.getElementById('google-fonts') as HTMLLinkElement;
      if (!fontLink) {
        const link = document.createElement('link');
        link.id = 'google-fonts';
        link.rel = 'stylesheet';
        link.href = `https://fonts.googleapis.com/css2?family=${themeConfig.font_family.replace(' ', '+')}:wght@300;400;500;600;700&display=swap`;
        document.head.appendChild(link);
      } else {
        fontLink.href = `https://fonts.googleapis.com/css2?family=${themeConfig.font_family.replace(' ', '+')}:wght@300;400;500;600;700&display=swap`;
      }
    }
  }, []);

  // Carregar tema da empresa
  const loadTheme = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await ApiService.getCustomization();
      if (response.success !== false) {
        const themeConfig = { ...defaultTheme, ...response };
        setTheme(themeConfig);
        applyTheme(themeConfig);
      }
    } catch (err) {
      console.error('Erro ao carregar tema:', err);
      setError('Não foi possível carregar o tema');
      // Aplicar tema padrão em caso de erro
      setTheme(defaultTheme);
      applyTheme(defaultTheme);
    } finally {
      setLoading(false);
    }
  }, [applyTheme]);

  // Carregar tema público por domínio
  const loadPublicTheme = useCallback(async (domain: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await ApiService.getPublicCustomization(domain);
      if (response.success !== false) {
        const themeConfig = { ...defaultTheme, ...response };
        setTheme(themeConfig);
        applyTheme(themeConfig);
      }
    } catch (err) {
      console.error('Erro ao carregar tema público:', err);
      setError('Não foi possível carregar o tema');
      // Aplicar tema padrão em caso de erro
      setTheme(defaultTheme);
      applyTheme(defaultTheme);
    } finally {
      setLoading(false);
    }
  }, [applyTheme]);

  // Atualizar tema
  const updateTheme = useCallback(async (newTheme: Partial<ThemeConfig>) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedTheme = { ...theme, ...newTheme };
      const response = await ApiService.updateCustomization(updatedTheme);
      
      if (response.success) {
        setTheme(updatedTheme);
        applyTheme(updatedTheme);
        return { success: true };
      } else {
        throw new Error(response.error || 'Erro ao atualizar tema');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao atualizar tema';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [theme, applyTheme]);

  // Resetar tema para padrão
  const resetTheme = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await ApiService.resetCustomization();
      
      if (response.success) {
        const resetTheme = response.data || defaultTheme;
        setTheme(resetTheme);
        applyTheme(resetTheme);
        return { success: true };
      } else {
        throw new Error(response.error || 'Erro ao resetar tema');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao resetar tema';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [applyTheme]);

  // Aplicar tema temporário (para preview)
  const previewTheme = useCallback((previewConfig: Partial<ThemeConfig>) => {
    const previewTheme = { ...theme, ...previewConfig };
    applyTheme(previewTheme);
  }, [theme, applyTheme]);

  // Reverter para tema salvo (cancelar preview)
  const revertPreview = useCallback(() => {
    applyTheme(theme);
  }, [theme, applyTheme]);

  // Inicializar tema na montagem do componente
  useEffect(() => {
    // Aplicar tema padrão imediatamente
    applyTheme(defaultTheme);
  }, [applyTheme]);

  return {
    theme,
    loading,
    error,
    loadTheme,
    loadPublicTheme,
    updateTheme,
    resetTheme,
    previewTheme,
    revertPreview,
    applyTheme
  };
};

// Hook para detectar domínio atual
export const useCurrentDomain = () => {
  const [domain, setDomain] = useState<string>('');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setDomain(window.location.hostname);
    }
  }, []);

  return domain;
};

// Hook para aplicar tema automaticamente baseado no domínio
export const useAutoTheme = () => {
  const { loadPublicTheme, theme, loading, error } = useTheme();
  const domain = useCurrentDomain();

  useEffect(() => {
    if (domain && domain !== 'localhost' && domain !== '127.0.0.1') {
      loadPublicTheme(domain);
    }
  }, [domain, loadPublicTheme]);

  return { theme, loading, error, domain };
};
