# Correção da API para o Domínio

## Problema identificado:
O frontend estava tentando acessar a API diretamente pelo IP `**************:3001` quando acessado pelo domínio, em vez de usar o proxy do Nginx.

## Mudanças realizadas:
✅ **src/lib/api.ts**: Alterado para usar `/api` (proxy do Nginx)  
✅ **src/hooks/useAuth.tsx**: Alterado para usar `/api` (proxy do Nginx)  
✅ **src/components/ContactForm.tsx**: Alterado para usar `/api` (proxy do Nginx)  

## Comandos para executar no servidor:

### 1. Parar o PM2 atual:
```bash
cd /opt/SollaraGarden
pm2 stop sollaragarden
```

### 2. Fazer pull das mudanças do Git:
```bash
git pull origin main
```

### 3. Reinstalar dependências (se necessário):
```bash
npm install
```

### 4. Reiniciar o PM2:
```bash
pm2 start sollaragarden
```

### 5. Verificar os logs:
```bash
pm2 logs sollaragarden --lines 10
```

### 6. Testar os acessos:

**Pelo IP:**
```bash
curl -I http://**************
curl -I http://**************/api/hero
```

**Pelo domínio:**
```bash
curl -I http://sollara-garden.gruposalha.com.br
curl -I http://sollara-garden.gruposalha.com.br/api/hero
```

## Resultado esperado:
Agora tanto o acesso pelo IP quanto pelo domínio devem funcionar corretamente:

- ✅ Frontend: http://sollara-garden.gruposalha.com.br
- ✅ API: http://sollara-garden.gruposalha.com.br/api
- ✅ Admin: http://sollara-garden.gruposalha.com.br/admin

## Arquivos modificados:
- `src/lib/api.ts`
- `src/hooks/useAuth.tsx` 
- `src/components/ContactForm.tsx`

Todos agora usam `/api` que é roteado pelo Nginx para `localhost:3001`. 