
import React, { useState, Suspense } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { ArrowLeft, Settings, Image, MessageSquare, Video, FileText, LogOut, User, Play, Menu, Building2, Shield } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import HeroEditor from '@/components/admin/HeroEditor';
import GalleryManager from '@/components/admin/GalleryManager';
import SubmissionManager from '@/components/admin/SubmissionManager';
import FooterEditor from '@/components/admin/FooterEditor';
import VideoManager from '@/components/admin/VideoManager';


// Loading fallback
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="w-8 h-8 border-4 border-luxury-gold border-t-transparent rounded-full animate-spin"></div>
  </div>
);

// Error Boundary Class Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: React.ReactNode; fallback: React.ComponentType<{ error: Error }> }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError && this.state.error) {
      const Fallback = this.props.fallback;
      return <Fallback error={this.state.error} />;
    }

    return this.props.children;
  }
}

// Componente de fallback para erros
const ErrorFallback = ({ error }: { error: Error }) => (
  <div className="p-8 text-center border border-red-200 bg-red-50 rounded-lg">
    <h3 className="text-lg font-semibold text-red-600 mb-2">Erro ao carregar componente</h3>
    <p className="text-gray-600 mb-4 text-sm">{error.message}</p>
    <Button onClick={() => window.location.reload()} variant="outline">
      Recarregar Página
    </Button>
  </div>
);

// Componente de teste simples para vídeos
const SimpleVideoTest = () => (
  <div className="p-6 border border-blue-200 bg-blue-50 rounded-lg">
    <h3 className="text-lg font-semibold text-blue-600 mb-2">Teste - Aba de Vídeos</h3>
    <p className="text-gray-600 mb-4">
      Se você está vendo esta mensagem, significa que a aba de vídeos está funcionando.
      O problema pode estar no componente VideoManager.
    </p>
    <div className="space-y-2">
      <p className="text-sm text-gray-500">Estado da aba: ✅ Funcional</p>
      <p className="text-sm text-gray-500">Renderização: ✅ OK</p>
      <p className="text-sm text-gray-500">Componente: 🔍 Investigando...</p>
    </div>
  </div>
);

const Admin = () => {
  const { logout, user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('hero');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [useVideoTest, setUseVideoTest] = useState(false);

  const handleLogout = () => {
    logout();
    toast({
      title: "Logout realizado com sucesso!",
      description: "Você foi desconectado do painel administrativo.",
    });
    navigate('/admin/login');
  };

  const toggleVideoTest = () => {
    setUseVideoTest(!useVideoTest);
  };

  const tabItems = [
    { value: 'hero', label: 'Seção Principal', icon: Video, shortLabel: 'Hero' },
    { value: 'videos', label: 'Vídeos', icon: Play, shortLabel: 'Vídeos' },
    { value: 'carousel', label: 'Galeria', icon: Image, shortLabel: 'Galeria' },
    { value: 'footer', label: 'Rodapé', icon: FileText, shortLabel: 'Rodapé' },
    { value: 'leads', label: 'Leads', icon: MessageSquare, shortLabel: 'Leads' },
  ];



  return (
    <div className="min-h-screen bg-gradient-to-br from-luxury-beige/20 to-luxury-cream/30">
      {/* Header */}
      <div className="bg-white/95 backdrop-blur-sm shadow-lg border-b border-luxury-gold/20 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            {/* Left Section */}
            <div className="flex items-center space-x-2 sm:space-x-4">
              {/* Mobile Menu Button */}
              <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="lg:hidden p-2">
                    <Menu className="w-5 h-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-72 p-0">
                  <div className="p-6 border-b border-luxury-gold/20">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-luxury-gold/20 rounded-lg">
                        <Building2 className="w-6 h-6 text-luxury-brown" />
                      </div>
                      <div>
                        <h2 className="font-bold text-luxury-brown">Painel Admin</h2>
                        <p className="text-sm text-luxury-brown/70">Sollara Garden</p>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 space-y-2">
                    {tabItems.map((item) => {
                      const Icon = item.icon;
                      return (
                        <Button
                          key={item.value}
                          variant={activeTab === item.value ? "default" : "ghost"}
                          className={`w-full justify-start ${
                            activeTab === item.value 
                              ? 'bg-luxury-gold text-luxury-brown' 
                              : 'hover:bg-luxury-gold/10 text-luxury-brown/80'
                          }`}
                          onClick={() => {
                            setActiveTab(item.value);
                            setSidebarOpen(false);
                          }}
                        >
                          <Icon className="w-4 h-4 mr-3" />
                          {item.label}
                        </Button>
                      );
                    })}
                  </div>
                </SheetContent>
              </Sheet>

              {/* Back to Site Button */}
              <Link to="/">
                <Button variant="ghost" size="sm" className="hidden sm:flex text-luxury-brown hover:text-luxury-gold hover:bg-luxury-gold/10">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Voltar ao Site</span>
                </Button>
              </Link>

              {/* Title */}
              <div className="flex items-center space-x-3">
                <div className="hidden sm:flex p-2 bg-luxury-gold/20 rounded-lg">
                  <Shield className="w-5 h-5 text-luxury-brown" />
                </div>
                <div>
                  <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-luxury-brown">
                    Painel Administrativo
                  </h1>
                  <p className="hidden sm:block text-sm text-luxury-brown/70">
                    Sollara Garden Barra Mansa
                  </p>
                </div>
              </div>
            </div>

            {/* Right Section */}
            <div className="flex items-center space-x-2 sm:space-x-3">
              {/* User Info */}
              <div className="hidden sm:flex items-center space-x-2 px-3 py-2 bg-luxury-gold/10 rounded-lg">
                <User className="w-4 h-4 text-luxury-brown/70" />
                <span className="text-sm font-medium text-luxury-brown">
                  {user?.username || 'admin'}
                </span>
              </div>

              {/* Logout Button */}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleLogout}
                className="border-luxury-gold/30 text-luxury-brown hover:bg-luxury-gold/10 hover:border-luxury-gold"
              >
                <LogOut className="w-4 h-4 sm:mr-2" />
                <span className="hidden sm:inline">Sair</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-4 sm:py-6 lg:py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          {/* Desktop Tabs */}
          <div className="hidden lg:block">
            <TabsList className="grid w-full grid-cols-5 h-12 bg-white/50 backdrop-blur-sm">
              {tabItems.map((item) => {
                const Icon = item.icon;
                return (
                  <TabsTrigger 
                    key={item.value}
                    value={item.value} 
                    className="flex items-center space-x-2 data-[state=active]:bg-luxury-gold data-[state=active]:text-luxury-brown"
                  >
                    <Icon className="w-4 h-4" />
                    <span className="hidden xl:inline">{item.label}</span>
                    <span className="xl:hidden">{item.shortLabel}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </div>

          {/* Mobile Tabs */}
          <div className="lg:hidden">
            <div className="flex items-center justify-between bg-white/50 backdrop-blur-sm rounded-lg p-2">
              <div className="flex space-x-1 overflow-x-auto scrollbar-hide">
                {tabItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Button
                      key={item.value}
                      variant={activeTab === item.value ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setActiveTab(item.value)}
                      className={`flex-shrink-0 ${
                        activeTab === item.value 
                          ? 'bg-luxury-gold text-luxury-brown' 
                          : 'text-luxury-brown/70'
                      }`}
                    >
                      <Icon className="w-4 h-4 sm:mr-2" />
                      <span className="hidden sm:inline text-xs">{item.shortLabel}</span>
                    </Button>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Tab Contents */}
          <TabsContent value="hero" className="space-y-6">
            <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-luxury-gold/20 rounded-lg">
                    <Video className="w-5 h-5 text-luxury-brown" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-luxury-brown">Editar Seção Principal</CardTitle>
                    <CardDescription className="text-luxury-brown/70">
                      Configure os textos, vídeo e imagem de fundo da seção principal
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <ErrorBoundary fallback={ErrorFallback}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <HeroEditor />
                  </Suspense>
                </ErrorBoundary>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="videos" className="space-y-6">
            <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-luxury-gold/20 rounded-lg">
                      <Play className="w-5 h-5 text-luxury-brown" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-luxury-brown">Gerenciar Vídeos</CardTitle>
                      <CardDescription className="text-luxury-brown/70">
                        Adicione, edite ou remova vídeos do site
                      </CardDescription>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={toggleVideoTest}
                    className="border-luxury-gold/30 text-luxury-brown hover:bg-luxury-gold/10"
                  >
                    {useVideoTest ? 'Usar VideoManager' : 'Testar Componente'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {useVideoTest ? (
                  <SimpleVideoTest />
                ) : (
                  <ErrorBoundary fallback={ErrorFallback}>
                    <Suspense fallback={<LoadingSpinner />}>
                      <VideoManager />
                    </Suspense>
                  </ErrorBoundary>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="carousel" className="space-y-6">
            <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-luxury-gold/20 rounded-lg">
                    <Image className="w-5 h-5 text-luxury-brown" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-luxury-brown">Gerenciar Galeria de Imagens</CardTitle>
                    <CardDescription className="text-luxury-brown/70">
                      Adicione, edite ou remova imagens da galeria
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <ErrorBoundary fallback={ErrorFallback}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <GalleryManager />
                  </Suspense>
                </ErrorBoundary>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="footer" className="space-y-6">
            <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-luxury-gold/20 rounded-lg">
                    <FileText className="w-5 h-5 text-luxury-brown" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-luxury-brown">Editar Rodapé</CardTitle>
                    <CardDescription className="text-luxury-brown/70">
                      Configure as informações do rodapé da página
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <ErrorBoundary fallback={ErrorFallback}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <FooterEditor />
                  </Suspense>
                </ErrorBoundary>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="leads" className="space-y-6">
            <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-luxury-gold/20 rounded-lg">
                    <MessageSquare className="w-5 h-5 text-luxury-brown" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-luxury-brown">Leads Capturados</CardTitle>
                    <CardDescription className="text-luxury-brown/70">
                      Visualize e exporte todos os contatos enviados pelo formulário
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <ErrorBoundary fallback={ErrorFallback}>
                  <Suspense fallback={<LoadingSpinner />}>
                    <SubmissionManager />
                  </Suspense>
                </ErrorBoundary>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Admin;
