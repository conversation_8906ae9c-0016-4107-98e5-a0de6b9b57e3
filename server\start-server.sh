#!/bin/bash

# Script para gerenciar o servidor Sollara Garden com PM2

echo "=== Sollara Garden Server Manager ==="

case "$1" in
  start)
    echo "Iniciando servidor Sollara Garden..."
    cd /opt/SollaraGarden/server
    pm2 start ecosystem.config.js
    ;;
  stop)
    echo "Parando servidor Sollara Garden..."
    pm2 stop sollara-garden-server
    ;;
  restart)
    echo "Reiniciando servidor Sollara Garden..."
    pm2 restart sollara-garden-server
    ;;
  logs)
    echo "Exibindo logs do servidor..."
    pm2 logs sollara-garden-server
    ;;
  status)
    echo "Status do servidor:"
    pm2 list | grep sollara-garden-server
    ;;
  *)
    echo "Uso: $0 {start|stop|restart|logs|status}"
    echo ""
    echo "Comandos disponíveis:"
    echo "  start   - Inicia o servidor"
    echo "  stop    - Para o servidor"  
    echo "  restart - Reinicia o servidor"
    echo "  logs    - Mostra os logs"
    echo "  status  - Mostra o status"
    exit 1
    ;;
esac 