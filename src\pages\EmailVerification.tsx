import { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/hooks/useAuth';
import { Mail, ArrowLeft, Shield, CheckCircle, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const EmailVerification = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [success, setSuccess] = useState(false);
  const [verified, setVerified] = useState(false);
  const [error, setError] = useState('');
  const [searchParams] = useSearchParams();
  const { verifyEmail, resendVerification } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const token = searchParams.get('token');

  useEffect(() => {
    // Se tem token na URL, tentar verificar automaticamente
    if (token) {
      handleAutoVerification();
    }
  }, [token]);

  const handleAutoVerification = async () => {
    if (!token) return;
    
    setVerifying(true);
    try {
      const response = await verifyEmail(token);
      
      if (response.success) {
        setVerified(true);
        toast({
          title: "Email verificado!",
          description: "Sua conta foi ativada com sucesso.",
        });

        // Redirecionar para login após 3 segundos
        setTimeout(() => {
          navigate('/admin-login');
        }, 3000);
      }
    } catch (error: any) {
      setError(error.message || 'Token inválido ou expirado');
      toast({
        title: "Erro na verificação",
        description: error.message || 'Token inválido ou expirado',
        variant: "destructive",
      });
    } finally {
      setVerifying(false);
    }
  };

  const handleResendVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await resendVerification(email);
      
      if (response.success) {
        setSuccess(true);
        
        toast({
          title: "Verificação reenviada!",
          description: "Verifique sua caixa de entrada.",
        });
      }
    } catch (error: any) {
      setError(error.message || 'Erro ao reenviar verificação');
      toast({
        title: "Erro",
        description: error.message || 'Erro ao reenviar verificação',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Se verificação automática foi bem-sucedida
  if (verified) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full bg-gradient-to-br from-luxury-gold/20 to-transparent"></div>
        </div>
        
        <div className="relative w-full max-w-md mx-auto z-10">
          <div className="mb-6 sm:mb-8 text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </div>
            
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-luxury-brown mb-2">
              Email Verificado!
            </h1>
            <p className="text-luxury-brown/70 text-sm sm:text-base">
              Sua conta foi ativada com sucesso
            </p>
          </div>

          <Card className="w-full shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <p className="text-luxury-brown">
                  Parabéns! Seu email foi verificado e sua conta está ativa. 
                  Você já pode fazer login no sistema.
                </p>
                
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <p className="text-sm text-green-800">
                    <strong>Redirecionamento automático em 3 segundos...</strong><br />
                    Ou clique no botão abaixo para ir agora.
                  </p>
                </div>

                <Link to="/admin-login">
                  <Button className="w-full bg-gradient-to-r from-luxury-gold to-luxury-gold-dark hover:from-luxury-gold-dark hover:to-luxury-gold text-luxury-brown">
                    Fazer Login
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Se está verificando
  if (verifying) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full bg-gradient-to-br from-luxury-gold/20 to-transparent"></div>
        </div>
        
        <div className="relative w-full max-w-md mx-auto z-10">
          <Card className="w-full shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center mb-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-luxury-gold"></div>
                </div>
                <h2 className="text-xl font-semibold text-luxury-brown">
                  Verificando seu email...
                </h2>
                <p className="text-luxury-brown/70">
                  Aguarde enquanto confirmamos sua verificação.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Se reenvio foi bem-sucedido
  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full bg-gradient-to-br from-luxury-gold/20 to-transparent"></div>
        </div>
        
        <div className="relative w-full max-w-md mx-auto z-10">
          <div className="mb-6 sm:mb-8 text-center">
            <Link to="/admin-login">
              <Button 
                variant="ghost" 
                size="sm" 
                className="mb-4 sm:mb-6 text-luxury-brown hover:text-luxury-gold hover:bg-luxury-gold/10 transition-all duration-300"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar ao Login
              </Button>
            </Link>
            
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-green-100 rounded-full">
                <Mail className="w-8 h-8 text-green-600" />
              </div>
            </div>
            
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-luxury-brown mb-2">
              Verificação Reenviada!
            </h1>
            <p className="text-luxury-brown/70 text-sm sm:text-base">
              Nova verificação foi enviada
            </p>
          </div>

          <Card className="w-full shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <p className="text-luxury-brown">
                  Uma nova verificação foi enviada para <strong>{email}</strong>. 
                  Verifique sua caixa de entrada e clique no link de verificação.
                </p>
                
                <div className="p-4 bg-luxury-gold/10 rounded-lg border border-luxury-gold/20">
                  <p className="text-sm text-luxury-brown/80">
                    <strong>Não recebeu o email?</strong><br />
                    Verifique sua caixa de spam ou aguarde alguns minutos.
                  </p>
                </div>

                <div className="flex flex-col gap-2">
                  <Button
                    onClick={() => setSuccess(false)}
                    variant="outline"
                    className="w-full"
                  >
                    Tentar Novamente
                  </Button>
                  
                  <Link to="/admin-login">
                    <Button variant="ghost" className="w-full">
                      Voltar ao Login
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Página de reenvio de verificação
  return (
    <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full bg-gradient-to-br from-luxury-gold/20 to-transparent"></div>
      </div>
      
      <div className="relative w-full max-w-md mx-auto z-10">
        <div className="mb-6 sm:mb-8 text-center">
          <Link to="/admin-login">
            <Button 
              variant="ghost" 
              size="sm" 
              className="mb-4 sm:mb-6 text-luxury-brown hover:text-luxury-gold hover:bg-luxury-gold/10 transition-all duration-300"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar ao Login
            </Button>
          </Link>
          
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-luxury-gold/20 rounded-full">
              <Shield className="w-8 h-8 text-luxury-brown" />
            </div>
          </div>
          
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-luxury-brown mb-2">
            Verificação de Email
          </h1>
          <p className="text-luxury-brown/70 text-sm sm:text-base">
            Reenviar email de verificação
          </p>
        </div>

        <Card className="w-full shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="space-y-2 pb-4">
            <CardTitle className="text-xl sm:text-2xl text-center flex items-center justify-center text-luxury-brown">
              <RefreshCw className="w-5 h-5 sm:w-6 sm:h-6 mr-2 text-luxury-gold" />
              Reenviar Verificação
            </CardTitle>
            <CardDescription className="text-center text-luxury-brown/70 text-sm sm:text-base">
              Digite seu email para receber nova verificação
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pt-0">
            <form onSubmit={handleResendVerification} className="space-y-4 sm:space-y-6">
              <div className="space-y-2">
                <Label 
                  htmlFor="email" 
                  className="text-luxury-brown font-medium text-sm sm:text-base"
                >
                  Email
                </Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-luxury-brown/50" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Digite seu email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10 sm:pl-12 h-11 sm:h-12 text-sm sm:text-base bg-luxury-beige/20 border-luxury-gold/30 focus:border-luxury-gold focus:ring-luxury-gold/20 rounded-xl"
                    required
                    disabled={loading}
                  />
                </div>
              </div>

              {error && (
                <Alert variant="destructive" className="border-red-200 bg-red-50">
                  <AlertDescription className="text-sm">{error}</AlertDescription>
                </Alert>
              )}

              <Button 
                type="submit" 
                className="w-full h-11 sm:h-12 text-sm sm:text-base font-semibold bg-gradient-to-r from-luxury-gold to-luxury-gold-dark hover:from-luxury-gold-dark hover:to-luxury-gold text-luxury-brown shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl"
                disabled={loading || !email.trim()}
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-luxury-brown mr-2"></div>
                    Reenviando...
                  </div>
                ) : (
                  'Reenviar Verificação'
                )}
              </Button>
            </form>

            <div className="mt-6 p-3 sm:p-4 bg-gradient-to-r from-luxury-gold/10 to-luxury-beige/20 rounded-xl border border-luxury-gold/20">
              <p className="text-xs sm:text-sm text-luxury-brown/80 text-center leading-relaxed">
                <strong className="text-luxury-brown">Importante:</strong><br />
                Você precisa verificar seu email antes de poder fazer login no sistema.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EmailVerification; 