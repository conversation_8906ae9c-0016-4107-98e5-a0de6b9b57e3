# 🏡 SOLLARA GARDEN - Landing Page

<div align="center">
  <img src="public/lovable-uploads/51ca6b6b-95b1-4314-bad6-7305c41b418e.png" alt="Sollara Garden Logo" width="300" />
  <p><em>Transformando sonhos em realidade há mais de 30 anos na região do Vale do Paraíba</em></p>
</div>

## 📋 Sobre o Projeto

**Sollara Garden** é um empreendimento imobiliário de luxo localizado em Barra Mansa, desenvolvido pelo Grupo Salha Empreendimentos. Esta aplicação web consiste em uma landing page moderna e responsiva com painel administrativo para gerenciamento de conteúdo.

### ✨ Características Principais

- **Design Luxuoso**: Interface elegante com paleta de cores dourada e marrom
- **Responsividade Total**: Experiência otimizada para dispositivos móveis e desktop
- **Painel Administrativo**: Gerenciamento completo de conteúdo
- **Galeria de Imagens**: Exibição dinâmica das áreas do empreendimento
- **Seção de Vídeos**: Integração com YouTube e vídeos locais
- **Formulário de Contato**: Captura de leads com gerenciamento no painel admin
- **Animações Suaves**: Efeitos visuais para melhor experiência do usuário

## 🛠️ Tecnologias Utilizadas

<div align="center">

| Frontend | Backend | Estilização | Ferramentas |
|:--------:|:-------:|:-----------:|:-----------:|
| React | Node.js | Tailwind CSS | Vite |
| TypeScript | Express | shadcn/ui | SQLite |
| React Router | JWT Auth | Lucide Icons | Better-SQLite3 |

</div>

## 📁 Estrutura do Projeto

```
sollara-garden/
├── public/             # Arquivos estáticos e imagens
├── server/             # Backend Express e SQLite
├── src/
│   ├── components/     # Componentes React reutilizáveis
│   ├── contexts/       # Contextos React para gerenciamento de estado
│   ├── hooks/          # Hooks personalizados
│   ├── lib/            # Utilitários e serviços
│   ├── pages/          # Páginas principais da aplicação
│   └── types/          # Definições de tipos TypeScript
└── ...
```

## 🚀 Instalação e Execução

### Pré-requisitos

- Node.js (v16+)
- npm ou yarn

### Passos para Instalação

1. **Clone o repositório**

```bash
git clone <URL_DO_REPOSITÓRIO>
cd sollara-garden-landing-19
```

2. **Instale as dependências**

```bash
npm install
```

3. **Inicie o servidor backend**

```bash
cd server
node server.cjs
```

4. **Inicie o frontend (em um novo terminal)**

```bash
npm run dev
```

5. **Acesse a aplicação**

- Frontend: [http://**************:5173](http://**************:5173)
- API Backend: [http://**************:3001/api](http://**************:3001/api)
- Painel Admin: [http://**************:5173/admin](http://**************:5173/admin)

## 🔐 Acesso ao Painel Administrativo

- **URL**: `/admin`
- **Usuário padrão**: admin
- **Senha padrão**: admin123

## 📱 Funcionalidades do Painel Admin

- **Gerenciamento de Galeria**: Upload, edição e ordenação de imagens
- **Gerenciamento de Vídeos**: Adição de vídeos do YouTube ou arquivos locais
- **Edição do Rodapé**: Personalização das informações de contato e da empresa
- **Gerenciamento de Leads**: Visualização e controle das submissões do formulário

## 📊 Banco de Dados

O projeto utiliza SQLite com as seguintes tabelas principais:

- `users`: Administradores do sistema
- `gallery_images`: Imagens da galeria
- `videos`: Vídeos do empreendimento
- `content_management`: Conteúdos dinâmicos (rodapé, etc.)
- `form_submissions`: Submissões do formulário de contato

## 🎨 Paleta de Cores

- **Dourado Luxo**: `#D4AF37`
- **Marrom Escuro**: `#3A2618`
- **Bege Claro**: `#F5F0E6`
- **Marrom Médio**: `#8B5A2B`
- **Branco**: `#FFFFFF`

## 📄 Licença

© 2025 Sollara Garden. Todos os direitos reservados.

---

<div align="center">
  <p>Desenvolvido com ❤️ para o Grupo Salha Empreendimentos</p>
</div>
