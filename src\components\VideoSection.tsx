
import React, { useRef, useEffect, useState } from 'react';
import { ApiService } from '@/lib/api';

interface Video {
  id: number;
  title: string;
  description: string;
  url?: string;
  video_url?: string;
  thumbnail_url: string;
  category?: string;
  position?: number;
  order_index?: number;
  active?: boolean;
  is_active?: number;
  video_type?: string;
  created_at: string;
}

const VideoSection = () => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const sectionRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isIntersecting, setIsIntersecting] = useState(false);

  // Função para normalizar os dados do vídeo
  const normalizeVideo = (video: any): Video => {
    return {
      ...video,
      url: video.url || video.video_url,
      active: video.active !== undefined ? video.active : (video.is_active ? Boolean(video.is_active) : true),
      position: video.position !== undefined ? video.position : (video.order_index || 0)
    };
  };

  // Função para obter URL do vídeo
  const getVideoUrl = (video: Video): string => {
    return video.url || video.video_url || '';
  };

  // Função para verificar se vídeo está ativo
  const isVideoActive = (video: Video): boolean => {
    if (video.active !== undefined) return video.active;
    if (video.is_active !== undefined) return Boolean(video.is_active);
    return true;
  };

  // Função para obter posição do vídeo
  const getVideoPosition = (video: Video): number => {
    return video.position !== undefined ? video.position : (video.order_index || 0);
  };

  // Carregar vídeos da API
  useEffect(() => {
    const loadVideos = async () => {
      try {
        const response = await ApiService.getPublicVideos();
        console.log('Resposta da API de vídeos públicos:', response);
        
        // Verifica se a resposta é um array ou se tem uma propriedade data
        let videosArray: Video[] = [];
        if (Array.isArray(response)) {
          videosArray = response;
        } else if (response && Array.isArray(response.data)) {
          videosArray = response.data;
        } else {
          console.warn('Resposta da API não é um array válido:', response);
          videosArray = [];
        }
        
        const normalizedVideos = videosArray.map(normalizeVideo);
        const activeVideos = normalizedVideos
          .filter(isVideoActive)
          .sort((a: Video, b: Video) => getVideoPosition(a) - getVideoPosition(b));
        
        console.log('Vídeos normalizados e filtrados:', activeVideos);
        setVideos(activeVideos);
        if (activeVideos.length > 0) {
          setCurrentVideoIndex(0);
        }
      } catch (error) {
        console.error('Erro ao carregar vídeos:', error);
        setVideos([]);
      } finally {
        setLoading(false);
      }
    };

    loadVideos();
  }, []);

  // Convert YouTube URL to embed format
  const getYouTubeEmbedUrl = (url: string) => {
    console.log('Original URL:', url);
    
    let videoId = '';
    
    if (url.includes('youtube.com/shorts/')) {
      const match = url.match(/shorts\/([a-zA-Z0-9_-]+)/);
      videoId = match ? match[1] : '';
    } else if (url.includes('youtu.be/')) {
      const match = url.match(/youtu\.be\/([a-zA-Z0-9_-]+)/);
      videoId = match ? match[1] : '';
    } else if (url.includes('youtube.com/watch')) {
      const match = url.match(/[?&]v=([a-zA-Z0-9_-]+)/);
      videoId = match ? match[1] : '';
    }
    
    console.log('Extracted video ID:', videoId);
    
    if (videoId) {
      const embedUrl = `https://www.youtube-nocookie.com/embed/${videoId}?autoplay=1&mute=1&controls=1&rel=0&modestbranding=1&playsinline=1`;
      console.log('Final embed URL:', embedUrl);
      return embedUrl;
    }
    
    return url;
  };

  // Intersection Observer for auto play/pause
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsIntersecting(entry.isIntersecting);
          
          const currentVideo = videos[currentVideoIndex];
          const videoUrl = getVideoUrl(currentVideo);
          if (currentVideo && videoUrl && !videoUrl.includes('youtube') && videoRef.current) {
            if (entry.isIntersecting) {
              videoRef.current.play().catch(console.log);
            } else {
              videoRef.current.pause();
            }
          }
        });
      },
      { 
        threshold: 0.3,
        rootMargin: '0px 0px -100px 0px'
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [videos, currentVideoIndex]);

  // Função para trocar de vídeo
  const nextVideo = () => {
    setCurrentVideoIndex((prev) => (prev + 1) % videos.length);
  };

  const prevVideo = () => {
    setCurrentVideoIndex((prev) => (prev - 1 + videos.length) % videos.length);
  };

  if (loading) {
    return (
      <section id="video-section" className="py-16 bg-gradient-to-br from-black via-gray-900 to-black relative overflow-hidden">
        <div className="container mx-auto px-4 md:px-8 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="aspect-video bg-gray-800 rounded-2xl flex items-center justify-center">
              <div className="w-16 h-16 border-4 border-luxury-gold border-t-transparent rounded-full animate-spin"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (!videos.length) return null;

  const currentVideo = videos[currentVideoIndex];

  return (
    <section id="video-section" ref={sectionRef} className="py-16 bg-gradient-to-br from-black via-gray-900 to-black relative overflow-hidden">
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Cabeçalho da seção */}
          <div className="text-center mb-12 animate-fade-in">
            <h2 className="section-title text-white">
              {currentVideo.title}
            </h2>
            <div className="w-32 h-1 bg-luxury-gold mx-auto mb-8"></div>
            {currentVideo.description && (
              <p className="section-subtitle text-white/80">
                {currentVideo.description}
              </p>
            )}
          </div>

          {/* Container do vídeo */}
          <div className="relative">
            <div className="relative bg-gradient-to-br from-gray-900 to-black rounded-2xl overflow-hidden shadow-xl">
              <div className="aspect-video relative">
                {(() => {
                  const videoUrl = getVideoUrl(currentVideo);
                  const isYouTube = videoUrl.includes('youtube') || videoUrl.includes('youtu.be');
                  
                  return isYouTube ? (
                    <div className="relative w-full h-full">
                      <iframe
                        key={currentVideo.id}
                        src={getYouTubeEmbedUrl(videoUrl)}
                        className="w-full h-full rounded-2xl"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        allowFullScreen
                        frameBorder="0"
                        title={currentVideo.title}
                        loading="lazy"
                        referrerPolicy="strict-origin-when-cross-origin"
                      />
                    </div>
                  ) : (
                    <div className="relative w-full h-full">
                      <video
                        key={currentVideo.id}
                        ref={videoRef}
                        src={videoUrl}
                        className="w-full h-full object-cover rounded-2xl"
                        autoPlay
                        muted
                        loop
                        playsInline
                        preload="metadata"
                      />
                    </div>
                  );
                })()}
              </div>

              {/* Navegação entre vídeos */}
              {videos.length > 1 && (
                <>
                  <button
                    onClick={prevVideo}
                    className="absolute left-2 md:left-4 top-1/2 -translate-y-1/2 bg-white/20 backdrop-blur-md rounded-full p-2 md:p-3 text-white hover:bg-white/30 transition-all duration-200 shadow-lg"
                    aria-label="Vídeo anterior"
                  >
                    <svg className="w-4 h-4 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  
                  <button
                    onClick={nextVideo}
                    className="absolute right-2 md:right-4 top-1/2 -translate-y-1/2 bg-white/20 backdrop-blur-md rounded-full p-2 md:p-3 text-white hover:bg-white/30 transition-all duration-200 shadow-lg"
                    aria-label="Próximo vídeo"
                  >
                    <svg className="w-4 h-4 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </>
              )}
            </div>

            {/* Indicadores de vídeos */}
            {videos.length > 1 && (
              <div className="flex justify-center space-x-3 mt-8">
                {videos.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentVideoIndex(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-200 ${
                      index === currentVideoIndex
                        ? 'bg-luxury-gold scale-125'
                        : 'bg-white/40 hover:bg-white/60'
                    }`}
                    aria-label={`Ir para vídeo ${index + 1}`}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default VideoSection;
