import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Users, 
  Building2, 
  DollarSign, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  XCircle,
  Eye,
  Settings
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';

interface Company {
  id: number;
  name: string;
  domain: string;
  email: string;
  phone?: string;
  status: 'trial' | 'active' | 'suspended' | 'expired';
  plan_name: string;
  plan_price: number;
  trial_ends_at?: string;
  subscription_ends_at?: string;
  leads_used_current_month: number;
  campaigns_active: number;
  created_at: string;
}

interface DashboardStats {
  totalCompanies: number;
  activeCompanies: number;
  trialCompanies: number;
  monthlyRevenue: number;
  totalLeads: number;
  newSignupsThisMonth: number;
}

const SuperAdmin = () => {
  const { user } = useAuth();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  // Verificar se é super admin
  if (user?.role !== 'super_admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert className="max-w-md">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            Acesso negado. Esta área é restrita para super administradores.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Carregar dados do dashboard
  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const [companiesRes, statsRes] = await Promise.all([
        fetch('/api/super-admin/companies', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        }),
        fetch('/api/super-admin/stats', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        })
      ]);

      if (companiesRes.ok && statsRes.ok) {
        const companiesData = await companiesRes.json();
        const statsData = await statsRes.json();
        setCompanies(companiesData);
        setStats(statsData);
      } else {
        toast.error('Erro ao carregar dados do dashboard');
      }
    } catch (error) {
      console.error('Erro:', error);
      toast.error('Erro interno do servidor');
    } finally {
      setLoading(false);
    }
  };

  // Ações nas empresas
  const handleCompanyAction = async (companyId: number, action: 'activate' | 'suspend' | 'delete') => {
    try {
      const response = await fetch(`/api/super-admin/companies/${companyId}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        toast.success(`Ação ${action} executada com sucesso`);
        fetchDashboardData(); // Recarregar dados
      } else {
        const data = await response.json();
        toast.error(data.error || 'Erro ao executar ação');
      }
    } catch (error) {
      console.error('Erro:', error);
      toast.error('Erro interno do servidor');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Ativo</Badge>;
      case 'trial':
        return <Badge className="bg-blue-100 text-blue-800">Trial</Badge>;
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800">Suspenso</Badge>;
      case 'expired':
        return <Badge className="bg-gray-100 text-gray-800">Expirado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">CaptaFlow - Super Admin</h1>
          <p className="text-gray-600">Painel de controle do sistema SaaS</p>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Building2 className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total de Empresas</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalCompanies}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Empresas Ativas</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.activeCompanies}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <DollarSign className="h-8 w-8 text-yellow-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Receita Mensal</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.monthlyRevenue)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Novos Cadastros</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.newSignupsThisMonth}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Tabs */}
        <Tabs defaultValue="companies" className="space-y-6">
          <TabsList>
            <TabsTrigger value="companies">Empresas</TabsTrigger>
            <TabsTrigger value="transactions">Transações</TabsTrigger>
            <TabsTrigger value="settings">Configurações</TabsTrigger>
          </TabsList>

          {/* Lista de Empresas */}
          <TabsContent value="companies">
            <Card>
              <CardHeader>
                <CardTitle>Empresas Cadastradas</CardTitle>
                <CardDescription>
                  Gerencie todas as empresas do sistema
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Empresa</TableHead>
                        <TableHead>Domínio</TableHead>
                        <TableHead>Plano</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Trial/Vencimento</TableHead>
                        <TableHead>Leads Mês</TableHead>
                        <TableHead>Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {companies.map((company) => (
                        <TableRow key={company.id}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{company.name}</p>
                              <p className="text-sm text-gray-500">{company.email}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{company.domain}</Badge>
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">{company.plan_name}</p>
                              <p className="text-sm text-gray-500">{formatCurrency(company.plan_price)}/mês</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(company.status)}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {company.status === 'trial' && company.trial_ends_at && (
                                <div className="flex items-center text-blue-600">
                                  <Clock className="h-4 w-4 mr-1" />
                                  Trial até {formatDate(company.trial_ends_at)}
                                </div>
                              )}
                              {company.status === 'active' && company.subscription_ends_at && (
                                <div className="flex items-center text-green-600">
                                  <CheckCircle className="h-4 w-4 mr-1" />
                                  Vence em {formatDate(company.subscription_ends_at)}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm">
                              {company.leads_used_current_month} leads
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Eye className="h-4 w-4" />
                              </Button>
                              {company.status === 'suspended' ? (
                                <Button 
                                  size="sm" 
                                  variant="outline"
                                  onClick={() => handleCompanyAction(company.id, 'activate')}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  Ativar
                                </Button>
                              ) : (
                                <Button 
                                  size="sm" 
                                  variant="outline"
                                  onClick={() => handleCompanyAction(company.id, 'suspend')}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  Suspender
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Transações */}
          <TabsContent value="transactions">
            <Card>
              <CardHeader>
                <CardTitle>Transações e Pagamentos</CardTitle>
                <CardDescription>
                  Gerencie pagamentos PIX e Stripe
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-center text-gray-500 py-8">
                  Funcionalidade em desenvolvimento...
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Configurações */}
          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Configurações do Sistema</CardTitle>
                <CardDescription>
                  Configure parâmetros globais do CaptaFlow
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <Alert>
                    <Settings className="h-4 w-4" />
                    <AlertDescription>
                      <strong>PIX Configurado:</strong> 53882441000120
                    </AlertDescription>
                  </Alert>
                  
                  <Alert>
                    <Settings className="h-4 w-4" />
                    <AlertDescription>
                      <strong>WhatsApp:</strong> (21) 98230-1476
                    </AlertDescription>
                  </Alert>

                  <Alert>
                    <Settings className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Trial Padrão:</strong> 14 dias gratuitos
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SuperAdmin; 