import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { ApiService } from "@/lib/api";
import {
  Palette,
  Type,
  Image,
  Eye,
  RotateCcw,
  Save,
  Upload,
  Monitor,
  Smartphone,
  Tablet
} from "lucide-react";

interface CustomizationData {
  id?: number;
  company_id?: number;
  logo_url?: string;
  favicon_url?: string;
  primary_color: string;
  secondary_color: string;
  background_color: string;
  text_color: string;
  font_family: string;
  custom_css?: string;
  updated_at?: string;
}

const ThemeEditor: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  const [customization, setCustomization] = useState<CustomizationData>({
    primary_color: '#1e40af',
    secondary_color: '#f59e0b',
    background_color: '#ffffff',
    text_color: '#1f2937',
    font_family: 'Inter'
  });

  const fontOptions = [
    { value: 'Inter', label: 'Inter (Padrão)' },
    { value: 'Roboto', label: 'Roboto' },
    { value: 'Open Sans', label: 'Open Sans' },
    { value: 'Lato', label: 'Lato' },
    { value: 'Montserrat', label: 'Montserrat' },
    { value: 'Poppins', label: 'Poppins' },
    { value: 'Nunito', label: 'Nunito' },
    { value: 'Source Sans Pro', label: 'Source Sans Pro' }
  ];

  useEffect(() => {
    loadCustomization();
  }, []);

  const loadCustomization = async () => {
    setLoading(true);
    try {
      const response = await ApiService.getCustomization();
      if (response.success !== false) {
        setCustomization(response);
      }
    } catch (error) {
      console.error('Erro ao carregar personalização:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as configurações de personalização",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await ApiService.updateCustomization(customization);
      if (response.success) {
        toast({
          title: "Sucesso!",
          description: "Personalização salva com sucesso",
        });
        // Aplicar mudanças em tempo real
        applyThemeChanges();
      }
    } catch (error) {
      console.error('Erro ao salvar personalização:', error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar as configurações",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    if (!confirm('Tem certeza que deseja resetar todas as personalizações para o padrão?')) {
      return;
    }

    setLoading(true);
    try {
      const response = await ApiService.resetCustomization();
      if (response.success) {
        setCustomization(response.data);
        toast({
          title: "Sucesso!",
          description: "Personalização resetada para o padrão",
        });
        applyThemeChanges();
      }
    } catch (error) {
      console.error('Erro ao resetar personalização:', error);
      toast({
        title: "Erro",
        description: "Não foi possível resetar as configurações",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const applyThemeChanges = () => {
    // Aplicar mudanças CSS em tempo real
    const root = document.documentElement;
    root.style.setProperty('--primary-color', customization.primary_color);
    root.style.setProperty('--secondary-color', customization.secondary_color);
    root.style.setProperty('--background-color', customization.background_color);
    root.style.setProperty('--text-color', customization.text_color);
    root.style.setProperty('--font-family', customization.font_family);
  };

  const updateField = (field: keyof CustomizationData, value: string) => {
    setCustomization(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validar tipo de arquivo
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Erro",
        description: "Tipo de arquivo não permitido. Use apenas imagens (JPEG, PNG, GIF, WebP)",
        variant: "destructive",
      });
      return;
    }

    // Validar tamanho (5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "Erro",
        description: "Arquivo muito grande. O tamanho máximo é 5MB",
        variant: "destructive",
      });
      return;
    }

    setUploading(true);
    try {
      const response = await ApiService.uploadLogo(file);
      if (response.success) {
        updateField('logo_url', response.data.url);
        toast({
          title: "Sucesso!",
          description: "Logo enviado com sucesso",
        });
      }
    } catch (error) {
      console.error('Erro ao fazer upload do logo:', error);
      toast({
        title: "Erro",
        description: "Não foi possível fazer upload do logo",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleFaviconUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validar tipo de arquivo
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/x-icon'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Erro",
        description: "Tipo de arquivo não permitido. Use apenas imagens ou arquivos .ico",
        variant: "destructive",
      });
      return;
    }

    // Validar tamanho (2MB para favicon)
    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: "Erro",
        description: "Arquivo muito grande. O tamanho máximo para favicon é 2MB",
        variant: "destructive",
      });
      return;
    }

    setUploading(true);
    try {
      const response = await ApiService.uploadFavicon(file);
      if (response.success) {
        updateField('favicon_url', response.data.url);
        toast({
          title: "Sucesso!",
          description: "Favicon enviado com sucesso",
        });
      }
    } catch (error) {
      console.error('Erro ao fazer upload do favicon:', error);
      toast({
        title: "Erro",
        description: "Não foi possível fazer upload do favicon",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const getPreviewClass = () => {
    switch (previewMode) {
      case 'tablet': return 'max-w-2xl';
      case 'mobile': return 'max-w-sm';
      default: return 'max-w-full';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Carregando configurações...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Editor de Temas</h2>
          <p className="text-muted-foreground">
            Personalize a aparência da sua landing page
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleReset} disabled={saving}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Resetar
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Salvando...' : 'Salvar'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Editor */}
        <div className="space-y-6">
          <Tabs defaultValue="colors" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="colors">
                <Palette className="h-4 w-4 mr-2" />
                Cores
              </TabsTrigger>
              <TabsTrigger value="typography">
                <Type className="h-4 w-4 mr-2" />
                Tipografia
              </TabsTrigger>
              <TabsTrigger value="images">
                <Image className="h-4 w-4 mr-2" />
                Imagens
              </TabsTrigger>
              <TabsTrigger value="advanced">
                <Monitor className="h-4 w-4 mr-2" />
                Avançado
              </TabsTrigger>
            </TabsList>

            {/* Cores */}
            <TabsContent value="colors" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Paleta de Cores</CardTitle>
                  <CardDescription>
                    Configure as cores principais do seu tema
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="primary_color">Cor Primária</Label>
                      <div className="flex gap-2">
                        <Input
                          id="primary_color"
                          type="color"
                          value={customization.primary_color}
                          onChange={(e) => updateField('primary_color', e.target.value)}
                          className="w-16 h-10 p-1"
                        />
                        <Input
                          value={customization.primary_color}
                          onChange={(e) => updateField('primary_color', e.target.value)}
                          placeholder="#1e40af"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="secondary_color">Cor Secundária</Label>
                      <div className="flex gap-2">
                        <Input
                          id="secondary_color"
                          type="color"
                          value={customization.secondary_color}
                          onChange={(e) => updateField('secondary_color', e.target.value)}
                          className="w-16 h-10 p-1"
                        />
                        <Input
                          value={customization.secondary_color}
                          onChange={(e) => updateField('secondary_color', e.target.value)}
                          placeholder="#f59e0b"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="background_color">Cor de Fundo</Label>
                      <div className="flex gap-2">
                        <Input
                          id="background_color"
                          type="color"
                          value={customization.background_color}
                          onChange={(e) => updateField('background_color', e.target.value)}
                          className="w-16 h-10 p-1"
                        />
                        <Input
                          value={customization.background_color}
                          onChange={(e) => updateField('background_color', e.target.value)}
                          placeholder="#ffffff"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="text_color">Cor do Texto</Label>
                      <div className="flex gap-2">
                        <Input
                          id="text_color"
                          type="color"
                          value={customization.text_color}
                          onChange={(e) => updateField('text_color', e.target.value)}
                          className="w-16 h-10 p-1"
                        />
                        <Input
                          value={customization.text_color}
                          onChange={(e) => updateField('text_color', e.target.value)}
                          placeholder="#1f2937"
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Tipografia */}
            <TabsContent value="typography" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Configurações de Fonte</CardTitle>
                  <CardDescription>
                    Escolha a fonte principal do seu site
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div>
                    <Label htmlFor="font_family">Família da Fonte</Label>
                    <Select
                      value={customization.font_family}
                      onValueChange={(value) => updateField('font_family', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione uma fonte" />
                      </SelectTrigger>
                      <SelectContent>
                        {fontOptions.map((font) => (
                          <SelectItem key={font.value} value={font.value}>
                            <span style={{ fontFamily: font.value }}>
                              {font.label}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Imagens */}
            <TabsContent value="images" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Logos e Ícones</CardTitle>
                  <CardDescription>
                    Configure os logos da sua marca
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Logo Principal */}
                  <div className="space-y-3">
                    <Label>Logo Principal</Label>
                    <div className="flex items-center gap-4">
                      {customization.logo_url && (
                        <div className="flex-shrink-0">
                          <img
                            src={customization.logo_url}
                            alt="Logo atual"
                            className="h-12 w-auto object-contain border rounded"
                          />
                        </div>
                      )}
                      <div className="flex-1 space-y-2">
                        <Input
                          value={customization.logo_url || ''}
                          onChange={(e) => updateField('logo_url', e.target.value)}
                          placeholder="https://exemplo.com/logo.png"
                        />
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            disabled={uploading}
                            onClick={() => document.getElementById('logo-upload')?.click()}
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            {uploading ? 'Enviando...' : 'Fazer Upload'}
                          </Button>
                          <input
                            id="logo-upload"
                            type="file"
                            accept="image/*"
                            onChange={handleLogoUpload}
                            className="hidden"
                          />
                        </div>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Formatos aceitos: JPEG, PNG, GIF, WebP. Tamanho máximo: 5MB
                    </p>
                  </div>

                  <Separator />

                  {/* Favicon */}
                  <div className="space-y-3">
                    <Label>Favicon</Label>
                    <div className="flex items-center gap-4">
                      {customization.favicon_url && (
                        <div className="flex-shrink-0">
                          <img
                            src={customization.favicon_url}
                            alt="Favicon atual"
                            className="h-8 w-8 object-contain border rounded"
                          />
                        </div>
                      )}
                      <div className="flex-1 space-y-2">
                        <Input
                          value={customization.favicon_url || ''}
                          onChange={(e) => updateField('favicon_url', e.target.value)}
                          placeholder="https://exemplo.com/favicon.ico"
                        />
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            disabled={uploading}
                            onClick={() => document.getElementById('favicon-upload')?.click()}
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            {uploading ? 'Enviando...' : 'Fazer Upload'}
                          </Button>
                          <input
                            id="favicon-upload"
                            type="file"
                            accept="image/*,.ico"
                            onChange={handleFaviconUpload}
                            className="hidden"
                          />
                        </div>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Formatos aceitos: ICO, PNG, JPEG. Tamanho máximo: 2MB. Recomendado: 32x32px
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Avançado */}
            <TabsContent value="advanced" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>CSS Personalizado</CardTitle>
                  <CardDescription>
                    Adicione CSS personalizado para customizações avançadas
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={customization.custom_css || ''}
                    onChange={(e) => updateField('custom_css', e.target.value)}
                    placeholder="/* Seu CSS personalizado aqui */&#10;.custom-class {&#10;  color: #000;&#10;}"
                    rows={10}
                    className="font-mono text-sm"
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Preview */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Preview em Tempo Real
                  </CardTitle>
                  <CardDescription>
                    Visualize as mudanças em tempo real
                  </CardDescription>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant={previewMode === 'desktop' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewMode('desktop')}
                  >
                    <Monitor className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewMode === 'tablet' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewMode('tablet')}
                  >
                    <Tablet className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewMode === 'mobile' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewMode('mobile')}
                  >
                    <Smartphone className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className={`mx-auto transition-all duration-300 ${getPreviewClass()}`}>
                <div
                  className="border rounded-lg p-6 space-y-4"
                  style={{
                    backgroundColor: customization.background_color,
                    color: customization.text_color,
                    fontFamily: customization.font_family
                  }}
                >
                  {/* Preview Header */}
                  <div className="flex items-center justify-between">
                    {customization.logo_url ? (
                      <img
                        src={customization.logo_url}
                        alt="Logo"
                        className="h-8 object-contain"
                      />
                    ) : (
                      <div
                        className="px-4 py-2 rounded font-bold"
                        style={{ backgroundColor: customization.primary_color, color: '#ffffff' }}
                      >
                        Sua Marca
                      </div>
                    )}
                    <nav className="flex gap-4 text-sm">
                      <a href="#" style={{ color: customization.text_color }}>Home</a>
                      <a href="#" style={{ color: customization.text_color }}>Sobre</a>
                      <a href="#" style={{ color: customization.text_color }}>Contato</a>
                    </nav>
                  </div>

                  <Separator />

                  {/* Preview Content */}
                  <div className="space-y-4">
                    <h1 className="text-2xl font-bold">
                      Título Principal
                    </h1>
                    <p className="text-sm opacity-80">
                      Este é um exemplo de como seu site ficará com as configurações atuais.
                      O texto se adapta automaticamente às cores escolhidas.
                    </p>

                    <div className="flex gap-2">
                      <button
                        className="px-4 py-2 rounded text-white text-sm font-medium"
                        style={{ backgroundColor: customization.primary_color }}
                      >
                        Botão Primário
                      </button>
                      <button
                        className="px-4 py-2 rounded text-white text-sm font-medium"
                        style={{ backgroundColor: customization.secondary_color }}
                      >
                        Botão Secundário
                      </button>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div className="p-3 rounded border">
                        <h3 className="font-semibold mb-2">Card Exemplo</h3>
                        <p className="opacity-70">
                          Conteúdo do card com a tipografia selecionada.
                        </p>
                      </div>
                      <div className="p-3 rounded border">
                        <h3 className="font-semibold mb-2">Outro Card</h3>
                        <p className="opacity-70">
                          Mais conteúdo para demonstração.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Status */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between text-sm">
                <span>Status:</span>
                <Badge variant="secondary">
                  {saving ? 'Salvando...' : 'Pronto'}
                </Badge>
              </div>
              {customization.updated_at && (
                <div className="flex items-center justify-between text-sm mt-2">
                  <span>Última atualização:</span>
                  <span className="text-muted-foreground">
                    {new Date(customization.updated_at).toLocaleString()}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ThemeEditor;
