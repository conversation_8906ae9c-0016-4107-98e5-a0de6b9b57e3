{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/aboutsection.tsx", "./src/components/companysection.tsx", "./src/components/contactform.tsx", "./src/components/differentialssection.tsx", "./src/components/financingsection.tsx", "./src/components/footer.tsx", "./src/components/herosection.tsx", "./src/components/imagecarousel.tsx", "./src/components/protectedroute.tsx", "./src/components/sportssection.tsx", "./src/components/videosection.tsx", "./src/components/admin/carouseleditor.tsx", "./src/components/admin/footereditor.tsx", "./src/components/admin/formsubmissions.tsx", "./src/components/admin/gallerymanager.tsx", "./src/components/admin/heroeditor.tsx", "./src/components/admin/multiimageupload.tsx", "./src/components/admin/submissionmanager.tsx", "./src/components/admin/videomanager.tsx", "./src/components/effects/customcursor.tsx", "./src/components/effects/floatingparticles.tsx", "./src/components/effects/liquidbutton.tsx", "./src/components/effects/scrollindicator.tsx", "./src/components/hero/herobackground.tsx", "./src/components/hero/herologo.tsx", "./src/components/hero/heroscrollhint.tsx", "./src/components/hero/herovideo.tsx", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/use-toast.ts", "./src/contexts/admincontext.tsx", "./src/data/defaultdata.ts", "./src/hooks/use-mobile.tsx", "./src/hooks/use-toast.ts", "./src/hooks/useauth.tsx", "./src/hooks/uselocalstorage.ts", "./src/integrations/supabase/types.ts", "./src/lib/api.ts", "./src/lib/utils.ts", "./src/pages/admin.tsx", "./src/pages/adminlogin.tsx", "./src/pages/index.tsx", "./src/pages/notfound.tsx", "./src/types/admin.ts"], "version": "5.6.3"}