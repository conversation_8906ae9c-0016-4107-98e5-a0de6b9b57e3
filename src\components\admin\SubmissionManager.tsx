import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { ApiService } from '@/lib/api';
import { Trash2, Mail, Phone, User, Calendar, Download } from 'lucide-react';

interface Submission {
  id: number;
  name: string;
  email: string;
  phone: string;
  message: string;
  status: string;
  created_at: string;
  updated_at: string;
}

const SubmissionManager = () => {
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchSubmissions();
  }, []);

  const fetchSubmissions = async () => {
    try {
      const data = await ApiService.getSubmissions();
      setSubmissions(data as Submission[]);
    } catch (error) {
      console.error('Erro ao buscar submissões:', error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar as submissões.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (id: number, status: string) => {
    try {
      await ApiService.updateSubmissionStatus(id, status);
      await fetchSubmissions();
      toast({
        title: "Sucesso",
        description: "Status atualizado com sucesso!",
      });
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o status.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Tem certeza que deseja excluir esta submissão?')) return;
    
    try {
      await ApiService.deleteSubmission(id);
      await fetchSubmissions();
      toast({
        title: "Sucesso",
        description: "Submissão excluída com sucesso!",
      });
    } catch (error) {
      console.error('Erro ao excluir submissão:', error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir a submissão.",
        variant: "destructive",
      });
    }
  };

  const exportToCsv = () => {
    const headers = ['Nome', 'Email', 'Telefone', 'Mensagem', 'Status', 'Data'];
    const csvContent = [
      headers.join(','),
      ...submissions.map(sub => [
        `"${sub.name}"`,
        `"${sub.email}"`,
        `"${sub.phone}"`,
        `"${sub.message}"`,
        `"${sub.status}"`,
        `"${new Date(sub.created_at).toLocaleString('pt-BR')}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `leads-sollara-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      pending: "outline",
      contacted: "default",
      converted: "secondary",
      rejected: "destructive"
    };

    const labels: Record<string, string> = {
      pending: "Pendente",
      contacted: "Contatado",
      converted: "Convertido",
      rejected: "Rejeitado"
    };

    return (
      <Badge variant={variants[status] || "outline"}>
        {labels[status] || status}
      </Badge>
    );
  };

  if (loading) {
    return <div className="animate-pulse">Carregando submissões...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Leads Capturados</h2>
          <p className="text-muted-foreground">
            Total: {submissions.length} leads
          </p>
        </div>
        <Button onClick={exportToCsv} variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Exportar CSV
        </Button>
      </div>

      <div className="grid gap-4">
        {submissions.length === 0 ? (
          <Card>
            <CardContent className="flex items-center justify-center h-32">
              <p className="text-muted-foreground">Nenhum lead encontrado</p>
            </CardContent>
          </Card>
        ) : (
          submissions.map((submission) => (
            <Card key={submission.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <CardTitle className="text-lg flex items-center">
                      <User className="w-4 h-4 mr-2" />
                      {submission.name}
                    </CardTitle>
                    <CardDescription className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Mail className="w-4 h-4 mr-1" />
                        {submission.email}
                      </span>
                      {submission.phone && (
                        <span className="flex items-center">
                          <Phone className="w-4 h-4 mr-1" />
                          {submission.phone}
                        </span>
                      )}
                      <span className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {new Date(submission.created_at).toLocaleString('pt-BR')}
                      </span>
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(submission.status)}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(submission.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {submission.message && (
                    <div>
                      <strong>Mensagem:</strong>
                      <p className="text-sm text-muted-foreground mt-1">
                        {submission.message}
                      </p>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium">Status:</label>
                    <Select
                      value={submission.status}
                      onValueChange={(value) => handleStatusChange(submission.id, value)}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Pendente</SelectItem>
                        <SelectItem value="contacted">Contatado</SelectItem>
                        <SelectItem value="converted">Convertido</SelectItem>
                        <SelectItem value="rejected">Rejeitado</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => window.open(`mailto:${submission.email}`)}
                    >
                      <Mail className="w-4 h-4 mr-1" />
                      Enviar Email
                    </Button>
                    {submission.phone && (
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => window.open(`https://wa.me/55${submission.phone.replace(/\D/g, '')}`)}
                      >
                        <Phone className="w-4 h-4 mr-1" />
                        WhatsApp
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default SubmissionManager; 