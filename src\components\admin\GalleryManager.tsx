import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { ApiService } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Trash2, Edit2, Plus, Eye, EyeOff, ChevronUp, ChevronDown } from 'lucide-react';

interface GalleryImage {
  id: number;
  title: string;
  description?: string;
  image_url: string;
  order_index: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const GalleryManager = () => {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingImage, setEditingImage] = useState<GalleryImage | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    image_url: '',
    order_index: 0,
    is_active: true
  });
  const { toast } = useToast();

  useEffect(() => {
    loadImages();
  }, []);

  const loadImages = async () => {
    try {
      const response = await ApiService.getGalleryImages();
      if (response.success) {
        setImages(response.data);
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: "Falha ao carregar imagens da galeria",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      if (!formData.title.trim() || !formData.image_url.trim()) {
        toast({
          title: "Erro",
          description: "Título e URL da imagem são obrigatórios",
          variant: "destructive",
        });
        return;
      }

      if (editingImage) {
        await ApiService.updateGalleryImage(editingImage.id, formData);
        toast({
          title: "Sucesso",
          description: "Imagem atualizada com sucesso!",
        });
      } else {
        await ApiService.createGalleryImage(formData);
        toast({
          title: "Sucesso",
          description: "Imagem adicionada à galeria!",
        });
      }

      setIsDialogOpen(false);
      resetForm();
      loadImages();
    } catch (error) {
      toast({
        title: "Erro",
        description: "Falha ao salvar imagem",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (image: GalleryImage) => {
    setEditingImage(image);
    setFormData({
      title: image.title,
      description: image.description || '',
      image_url: image.image_url,
      order_index: image.order_index,
      is_active: image.is_active
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Tem certeza que deseja excluir esta imagem?')) return;

    try {
      await ApiService.deleteGalleryImage(id);
      toast({
        title: "Sucesso",
        description: "Imagem excluída com sucesso!",
      });
      loadImages();
    } catch (error) {
      toast({
        title: "Erro",
        description: "Falha ao excluir imagem",
        variant: "destructive",
      });
    }
  };

  const toggleActive = async (image: GalleryImage) => {
    try {
      await ApiService.updateGalleryImage(image.id, {
        ...image,
        is_active: !image.is_active
      });
      loadImages();
      toast({
        title: "Sucesso",
        description: `Imagem ${!image.is_active ? 'ativada' : 'desativada'} com sucesso!`,
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Falha ao alterar status da imagem",
        variant: "destructive",
      });
    }
  };

  const moveImage = async (image: GalleryImage, direction: 'up' | 'down') => {
    const currentIndex = images.findIndex(img => img.id === image.id);
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    if (targetIndex < 0 || targetIndex >= images.length) return;

    const targetImage = images[targetIndex];
    
    try {
      // Trocar as posições
      await ApiService.updateGalleryImage(image.id, {
        ...image,
        order_index: targetImage.order_index
      });
      
      await ApiService.updateGalleryImage(targetImage.id, {
        ...targetImage,
        order_index: image.order_index
      });

      loadImages();
      toast({
        title: "Sucesso",
        description: "Ordem das imagens atualizada!",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Falha ao reordenar imagens",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      image_url: '',
      order_index: images.length,
      is_active: true
    });
    setEditingImage(null);
  };

  const handleDialogOpenChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      resetForm();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-4 border-luxury-gold border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-luxury-brown">Galeria de Imagens</h2>
          <p className="text-luxury-brown-light">Gerencie as imagens exibidas no carrossel da página principal</p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
          <DialogTrigger asChild>
            <Button className="bg-luxury-gold hover:bg-luxury-gold/90 text-luxury-brown">
              <Plus className="w-4 h-4 mr-2" />
              Nova Imagem
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="text-luxury-brown">
                {editingImage ? 'Editar Imagem' : 'Nova Imagem'}
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="title" className="text-luxury-brown">Título *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Título da imagem"
                  className="border-luxury-gold/30 focus:border-luxury-gold"
                />
              </div>

              <div>
                <Label htmlFor="description" className="text-luxury-brown">Descrição</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Descrição da imagem (opcional)"
                  className="border-luxury-gold/30 focus:border-luxury-gold"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="image_url" className="text-luxury-brown">URL da Imagem *</Label>
                <Input
                  id="image_url"
                  value={formData.image_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
                  placeholder="https://exemplo.com/imagem.jpg"
                  className="border-luxury-gold/30 focus:border-luxury-gold"
                />
              </div>

              <div>
                <Label htmlFor="order_index" className="text-luxury-brown">Ordem</Label>
                <Input
                  id="order_index"
                  type="number"
                  value={formData.order_index}
                  onChange={(e) => setFormData(prev => ({ ...prev, order_index: parseInt(e.target.value) || 0 }))}
                  className="border-luxury-gold/30 focus:border-luxury-gold"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                />
                <Label htmlFor="is_active" className="text-luxury-brown">Imagem ativa</Label>
              </div>

              <div className="flex space-x-2 pt-4">
                <Button
                  onClick={handleSubmit}
                  className="flex-1 bg-luxury-gold hover:bg-luxury-gold/90 text-luxury-brown"
                >
                  {editingImage ? 'Atualizar' : 'Adicionar'}
                </Button>
                <Button
                  onClick={() => handleDialogOpenChange(false)}
                  variant="outline"
                  className="flex-1 border-luxury-gold/30 text-luxury-brown hover:bg-luxury-gold/10"
                >
                  Cancelar
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {images.length === 0 ? (
        <Card className="border-luxury-gold/30">
          <CardContent className="p-8 text-center">
            <p className="text-luxury-brown-light">Nenhuma imagem na galeria. Adicione uma imagem para começar.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {images.map((image, index) => (
            <Card key={image.id} className="border-luxury-gold/30 overflow-hidden">
              <div className="aspect-video relative">
                <img
                  src={image.image_url}
                  alt={image.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/placeholder.svg';
                  }}
                />
                <div className="absolute top-2 right-2 flex space-x-1">
                  <Badge variant={image.is_active ? "default" : "secondary"} className="text-xs">
                    {image.is_active ? 'Ativa' : 'Inativa'}
                  </Badge>
                </div>
              </div>
              
              <CardHeader className="pb-2">
                <CardTitle className="text-sm text-luxury-brown line-clamp-1">
                  {image.title}
                </CardTitle>
                {image.description && (
                  <p className="text-xs text-luxury-brown-light line-clamp-2">
                    {image.description}
                  </p>
                )}
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-luxury-brown-light">
                    Ordem: {image.order_index}
                  </span>
                  
                  <div className="flex space-x-1">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => moveImage(image, 'up')}
                      disabled={index === 0}
                      className="h-6 w-6 p-0 border-luxury-gold/30"
                    >
                      <ChevronUp className="w-3 h-3" />
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => moveImage(image, 'down')}
                      disabled={index === images.length - 1}
                      className="h-6 w-6 p-0 border-luxury-gold/30"
                    >
                      <ChevronDown className="w-3 h-3" />
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => toggleActive(image)}
                      className="h-6 w-6 p-0 border-luxury-gold/30"
                    >
                      {image.is_active ? (
                        <EyeOff className="w-3 h-3" />
                      ) : (
                        <Eye className="w-3 h-3" />
                      )}
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(image)}
                      className="h-6 w-6 p-0 border-luxury-gold/30"
                    >
                      <Edit2 className="w-3 h-3" />
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(image.id)}
                      className="h-6 w-6 p-0 border-luxury-gold/30 hover:border-red-300 hover:text-red-600"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default GalleryManager; 