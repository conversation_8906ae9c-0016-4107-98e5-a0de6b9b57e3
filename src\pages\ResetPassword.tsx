import { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/hooks/useAuth';
import { Lock, ArrowLeft, Shield, CheckCircle, Eye, EyeOff } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const ResetPassword = () => {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [searchParams] = useSearchParams();
  const { resetPassword } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      navigate('/forgot-password');
    }
  }, [token, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validações
    if (newPassword.length < 6) {
      setError('A senha deve ter pelo menos 6 caracteres');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('As senhas não conferem');
      return;
    }

    if (!token) {
      setError('Token inválido');
      return;
    }

    setLoading(true);

    try {
      const response = await resetPassword(token, newPassword);
      
      if (response.success) {
        setSuccess(true);
        
        toast({
          title: "Senha redefinida!",
          description: "Sua senha foi alterada com sucesso.",
        });

        // Redirecionar para login após 3 segundos
        setTimeout(() => {
          navigate('/admin-login');
        }, 3000);
      }
    } catch (error: any) {
      setError(error.message || 'Erro ao redefinir senha');
      toast({
        title: "Erro",
        description: error.message || 'Erro ao redefinir senha',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full bg-gradient-to-br from-luxury-gold/20 to-transparent"></div>
        </div>
        
        <div className="relative w-full max-w-md mx-auto z-10">
          <div className="mb-6 sm:mb-8 text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </div>
            
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-luxury-brown mb-2">
              Senha Redefinida!
            </h1>
            <p className="text-luxury-brown/70 text-sm sm:text-base">
              Sua senha foi alterada com sucesso
            </p>
          </div>

          <Card className="w-full shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <p className="text-luxury-brown">
                  Sua senha foi redefinida com sucesso! Você já pode fazer login com sua nova senha.
                </p>
                
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <p className="text-sm text-green-800">
                    <strong>Redirecionamento automático em 3 segundos...</strong><br />
                    Ou clique no botão abaixo para ir agora.
                  </p>
                </div>

                <Link to="/admin-login">
                  <Button className="w-full bg-gradient-to-r from-luxury-gold to-luxury-gold-dark hover:from-luxury-gold-dark hover:to-luxury-gold text-luxury-brown">
                    Ir para Login
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!token) {
    return null; // Será redirecionado pelo useEffect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full bg-gradient-to-br from-luxury-gold/20 to-transparent"></div>
      </div>
      
      <div className="relative w-full max-w-md mx-auto z-10">
        <div className="mb-6 sm:mb-8 text-center">
          <Link to="/admin-login">
            <Button 
              variant="ghost" 
              size="sm" 
              className="mb-4 sm:mb-6 text-luxury-brown hover:text-luxury-gold hover:bg-luxury-gold/10 transition-all duration-300"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar ao Login
            </Button>
          </Link>
          
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-luxury-gold/20 rounded-full">
              <Shield className="w-8 h-8 text-luxury-brown" />
            </div>
          </div>
          
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-luxury-brown mb-2">
            Nova Senha
          </h1>
          <p className="text-luxury-brown/70 text-sm sm:text-base">
            Digite sua nova senha de acesso
          </p>
        </div>

        <Card className="w-full shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="space-y-2 pb-4">
            <CardTitle className="text-xl sm:text-2xl text-center flex items-center justify-center text-luxury-brown">
              <Lock className="w-5 h-5 sm:w-6 sm:h-6 mr-2 text-luxury-gold" />
              Redefinir Senha
            </CardTitle>
            <CardDescription className="text-center text-luxury-brown/70 text-sm sm:text-base">
              Escolha uma senha segura para sua conta
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pt-0">
            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
              {/* New Password Field */}
              <div className="space-y-2">
                <Label 
                  htmlFor="newPassword" 
                  className="text-luxury-brown font-medium text-sm sm:text-base"
                >
                  Nova Senha
                </Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-luxury-brown/50" />
                  <Input
                    id="newPassword"
                    type={showNewPassword ? "text" : "password"}
                    placeholder="Digite sua nova senha"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="pl-10 sm:pl-12 pr-10 sm:pr-12 h-11 sm:h-12 text-sm sm:text-base bg-luxury-beige/20 border-luxury-gold/30 focus:border-luxury-gold focus:ring-luxury-gold/20 rounded-xl"
                    required
                    disabled={loading}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-luxury-gold/10"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    disabled={loading}
                  >
                    {showNewPassword ? (
                      <EyeOff className="h-4 w-4 text-luxury-brown/50" />
                    ) : (
                      <Eye className="h-4 w-4 text-luxury-brown/50" />
                    )}
                  </Button>
                </div>
                {newPassword && newPassword.length < 6 && (
                  <p className="text-xs text-orange-600">
                    A senha deve ter pelo menos 6 caracteres
                  </p>
                )}
              </div>

              {/* Confirm Password Field */}
              <div className="space-y-2">
                <Label 
                  htmlFor="confirmPassword" 
                  className="text-luxury-brown font-medium text-sm sm:text-base"
                >
                  Confirmar Nova Senha
                </Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-luxury-brown/50" />
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirme sua nova senha"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="pl-10 sm:pl-12 pr-10 sm:pr-12 h-11 sm:h-12 text-sm sm:text-base bg-luxury-beige/20 border-luxury-gold/30 focus:border-luxury-gold focus:ring-luxury-gold/20 rounded-xl"
                    required
                    disabled={loading}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-luxury-gold/10"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={loading}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4 text-luxury-brown/50" />
                    ) : (
                      <Eye className="h-4 w-4 text-luxury-brown/50" />
                    )}
                  </Button>
                </div>
                {confirmPassword && newPassword !== confirmPassword && (
                  <p className="text-xs text-red-600">
                    As senhas não conferem
                  </p>
                )}
              </div>

              {error && (
                <Alert variant="destructive" className="border-red-200 bg-red-50">
                  <AlertDescription className="text-sm">{error}</AlertDescription>
                </Alert>
              )}

              <Button 
                type="submit" 
                className="w-full h-11 sm:h-12 text-sm sm:text-base font-semibold bg-gradient-to-r from-luxury-gold to-luxury-gold-dark hover:from-luxury-gold-dark hover:to-luxury-gold text-luxury-brown shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl"
                disabled={loading || newPassword.length < 6 || newPassword !== confirmPassword}
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-luxury-brown mr-2"></div>
                    Redefinindo...
                  </div>
                ) : (
                  'Redefinir Senha'
                )}
              </Button>
            </form>

            <div className="mt-6 p-3 sm:p-4 bg-gradient-to-r from-luxury-gold/10 to-luxury-beige/20 rounded-xl border border-luxury-gold/20">
              <p className="text-xs sm:text-sm text-luxury-brown/80 text-center leading-relaxed">
                <strong className="text-luxury-brown">Dicas de segurança:</strong><br />
                Use uma senha forte com pelo menos 6 caracteres, incluindo letras e números.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ResetPassword; 