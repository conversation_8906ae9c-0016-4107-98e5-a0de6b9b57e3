const Database = require('better-sqlite3');
const path = require('path');

const dbPath = process.env.NODE_ENV === 'production' ? ':memory:' : path.join(__dirname, 'database.sqlite');
const db = new Database(dbPath);

console.log('🔄 Iniciando migração do banco de dados para estrutura SaaS...');

// Função para verificar se uma coluna existe
const columnExists = (tableName, columnName) => {
  try {
    const columns = db.prepare(`PRAGMA table_info(${tableName})`).all();
    return columns.some(col => col.name === columnName);
  } catch (error) {
    return false;
  }
};

// Função para verificar se uma tabela existe
const tableExists = (tableName) => {
  try {
    const result = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`).get(tableName);
    return !!result;
  } catch (error) {
    return false;
  }
};

// Migração 1: Adicionar colunas à tabela users
console.log('📝 Migração 1: Atualizando tabela users...');
try {
  if (!columnExists('users', 'company_id')) {
    db.exec('ALTER TABLE users ADD COLUMN company_id INTEGER DEFAULT NULL');
    console.log('✅ Coluna company_id adicionada à tabela users');
  }
  
  if (!columnExists('users', 'status')) {
    db.exec('ALTER TABLE users ADD COLUMN status TEXT DEFAULT "active"');
    console.log('✅ Coluna status adicionada à tabela users');
  }
  
  if (!columnExists('users', 'last_login')) {
    db.exec('ALTER TABLE users ADD COLUMN last_login DATETIME');
    console.log('✅ Coluna last_login adicionada à tabela users');
  }
} catch (error) {
  console.error('❌ Erro na migração da tabela users:', error.message);
}

// Migração 2: Adicionar coluna company_id às outras tabelas
console.log('📝 Migração 2: Atualizando tabelas para multi-tenant...');
try {
  // form_submissions
  if (tableExists('form_submissions') && !columnExists('form_submissions', 'company_id')) {
    db.exec('ALTER TABLE form_submissions ADD COLUMN company_id INTEGER DEFAULT 1');
    db.exec('ALTER TABLE form_submissions ADD COLUMN source_page TEXT');
    db.exec('ALTER TABLE form_submissions ADD COLUMN user_agent TEXT');
    console.log('✅ Tabela form_submissions atualizada para multi-tenant');
  }
  
  // gallery_images
  if (tableExists('gallery_images') && !columnExists('gallery_images', 'company_id')) {
    db.exec('ALTER TABLE gallery_images ADD COLUMN company_id INTEGER DEFAULT NULL');
    console.log('✅ Tabela gallery_images atualizada para multi-tenant');
  }
  
  // videos
  if (tableExists('videos') && !columnExists('videos', 'company_id')) {
    db.exec('ALTER TABLE videos ADD COLUMN company_id INTEGER DEFAULT NULL');
    console.log('✅ Tabela videos atualizada para multi-tenant');
  }
  
  // content_management
  if (tableExists('content_management') && !columnExists('content_management', 'company_id')) {
    db.exec('ALTER TABLE content_management ADD COLUMN company_id INTEGER DEFAULT NULL');
    console.log('✅ Tabela content_management atualizada para multi-tenant');
  }
} catch (error) {
  console.error('❌ Erro na migração multi-tenant:', error.message);
}

// Migração 3: Criar novas tabelas SaaS
console.log('📝 Migração 3: Criando novas tabelas SaaS...');
try {
  // Tabela de planos
  if (!tableExists('plans')) {
    db.exec(`
      CREATE TABLE plans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        price DECIMAL(10,2) NOT NULL,
        currency TEXT DEFAULT 'BRL',
        max_campaigns INTEGER DEFAULT 1,
        max_leads_per_month INTEGER DEFAULT 1000,
        features TEXT,
        stripe_price_id TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Tabela plans criada');
  }
  
  // Tabela de empresas
  if (!tableExists('companies')) {
    db.exec(`
      CREATE TABLE companies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        domain TEXT UNIQUE,
        email TEXT NOT NULL,
        phone TEXT,
        plan_id INTEGER NOT NULL,
        status TEXT DEFAULT 'trial',
        trial_ends_at DATETIME,
        subscription_ends_at DATETIME,
        leads_used_current_month INTEGER DEFAULT 0,
        campaigns_active INTEGER DEFAULT 0,
        whitelabel_config TEXT,
        stripe_customer_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (plan_id) REFERENCES plans (id)
      )
    `);
    console.log('✅ Tabela companies criada');
  }
  
  // Tabela de transações
  if (!tableExists('transactions')) {
    db.exec(`
      CREATE TABLE transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL,
        plan_id INTEGER NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency TEXT DEFAULT 'BRL',
        payment_method TEXT NOT NULL,
        stripe_payment_intent_id TEXT,
        pix_key TEXT DEFAULT '53882441000120',
        pix_qr_code TEXT,
        pix_receipt_url TEXT,
        status TEXT DEFAULT 'pending',
        paid_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id),
        FOREIGN KEY (plan_id) REFERENCES plans (id)
      )
    `);
    console.log('✅ Tabela transactions criada');
  }
  
  // Tabela de customizações
  if (!tableExists('company_customizations')) {
    db.exec(`
      CREATE TABLE company_customizations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL,
        logo_url TEXT,
        favicon_url TEXT,
        primary_color TEXT DEFAULT '#1e40af',
        secondary_color TEXT DEFAULT '#f59e0b',
        background_color TEXT DEFAULT '#ffffff',
        text_color TEXT DEFAULT '#1f2937',
        font_family TEXT DEFAULT 'Inter',
        custom_css TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id),
        UNIQUE(company_id)
      )
    `);
    console.log('✅ Tabela company_customizations criada');
  }
} catch (error) {
  console.error('❌ Erro na criação das novas tabelas:', error.message);
}

// Migração 4: Inserir dados padrão
console.log('📝 Migração 4: Inserindo dados padrão...');
try {
  // Inserir planos padrão
  const existingPlans = db.prepare('SELECT COUNT(*) as count FROM plans').get();
  if (existingPlans.count === 0) {
    const stmt = db.prepare(`
      INSERT INTO plans (name, price, max_campaigns, max_leads_per_month, features)
      VALUES (?, ?, ?, ?, ?)
    `);

    const plans = [
      {
        name: 'Essencial',
        price: 149.00,
        max_campaigns: 1,
        max_leads_per_month: 1000,
        features: JSON.stringify([
          '1 campanha ativa',
          'Até 1.000 leads/mês',
          'Painel administrativo',
          'Relatórios básicos',
          'Suporte por email'
        ])
      },
      {
        name: 'Growth',
        price: 299.00,
        max_campaigns: 3,
        max_leads_per_month: 5000,
        features: JSON.stringify([
          '3 campanhas ativas',
          'Até 5.000 leads/mês',
          'Domínio personalizado',
          'Relatórios avançados',
          'Integrações WhatsApp/CRM',
          'Suporte prioritário'
        ])
      },
      {
        name: 'Agency',
        price: 699.00,
        max_campaigns: -1,
        max_leads_per_month: -1,
        features: JSON.stringify([
          'Campanhas ilimitadas',
          'Leads ilimitados',
          'White-label disponível',
          'API completa',
          'Múltiplos usuários',
          'Gerente dedicado'
        ])
      }
    ];

    plans.forEach(plan => {
      stmt.run(plan.name, plan.price, plan.max_campaigns, plan.max_leads_per_month, plan.features);
    });
    console.log('✅ Planos padrão inseridos');
  }
  
  // Criar empresa demonstrativa padrão (ID 1)
  const existingDemoCompany = db.prepare('SELECT * FROM companies WHERE id = ?').get(1);
  if (!existingDemoCompany) {
    const demoCompanyStmt = db.prepare(`
      INSERT INTO companies (id, name, domain, email, phone, plan_id, status, trial_ends_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const trialEnd = new Date();
    trialEnd.setDate(trialEnd.getDate() + 14);
    
    demoCompanyStmt.run(
      1,
      'Empresa Demonstrativa',
      'localhost',
      '<EMAIL>',
      '(21) 98230-1476',
      2, // Growth plan
      'active',
      trialEnd.toISOString()
    );
    console.log('✅ Empresa demonstrativa criada');
  }
  
  // Atualizar usuário admin existente
  const existingAdmin = db.prepare('SELECT * FROM users WHERE username = ?').get('admin');
  if (existingAdmin) {
    db.prepare('UPDATE users SET role = ?, company_id = ? WHERE username = ?')
      .run('cliente_admin', 1, 'admin');
    console.log('✅ Usuário admin atualizado para cliente_admin da empresa demonstrativa');
  }
  
} catch (error) {
  console.error('❌ Erro na inserção de dados padrão:', error.message);
}

console.log('🎉 Migração concluída com sucesso!');
console.log('');
console.log('📊 Resumo da migração:');
console.log('- ✅ Estrutura multi-tenant implementada');
console.log('- ✅ Tabelas SaaS criadas (plans, companies, transactions, customizations)');
console.log('- ✅ Planos CaptaFlow configurados (Essencial R$149, Growth R$299, Agency R$699)');
console.log('- ✅ Empresa demonstrativa criada');
console.log('- ✅ PIX configurado: 53882441000120');
console.log('');
console.log('🔑 Acessos:');
console.log('- Usuario Admin: admin / admin123 (empresa demonstrativa)');

db.close(); 