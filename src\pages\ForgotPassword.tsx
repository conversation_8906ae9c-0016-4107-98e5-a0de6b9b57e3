import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/hooks/useAuth';
import { Mail, ArrowLeft, Shield, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [resetToken, setResetToken] = useState(''); // Para desenvolvimento
  const { forgotPassword } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await forgotPassword(email);
      
      if (response.success) {
        setSuccess(true);
        setResetToken(response.resetToken || ''); // Para desenvolvimento
        
        toast({
          title: "Email enviado!",
          description: "Verifique sua caixa de entrada para redefinir sua senha.",
        });
      }
    } catch (error: any) {
      setError(error.message || 'Erro ao solicitar recuperação de senha');
      toast({
        title: "Erro",
        description: error.message || 'Erro ao solicitar recuperação de senha',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGoToReset = () => {
    if (resetToken) {
      navigate(`/reset-password?token=${resetToken}`);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full bg-gradient-to-br from-luxury-gold/20 to-transparent"></div>
        </div>
        
        <div className="relative w-full max-w-md mx-auto z-10">
          <div className="mb-6 sm:mb-8 text-center">
            <Link to="/admin-login">
              <Button 
                variant="ghost" 
                size="sm" 
                className="mb-4 sm:mb-6 text-luxury-brown hover:text-luxury-gold hover:bg-luxury-gold/10 transition-all duration-300"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar ao Login
              </Button>
            </Link>
            
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </div>
            
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-luxury-brown mb-2">
              Email Enviado!
            </h1>
            <p className="text-luxury-brown/70 text-sm sm:text-base">
              Verificação de recuperação de senha
            </p>
          </div>

          <Card className="w-full shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <p className="text-luxury-brown">
                  Se o email <strong>{email}</strong> estiver cadastrado em nosso sistema, 
                  você receberá instruções para redefinir sua senha.
                </p>
                
                <div className="p-4 bg-luxury-gold/10 rounded-lg border border-luxury-gold/20">
                  <p className="text-sm text-luxury-brown/80">
                    <strong>Não recebeu o email?</strong><br />
                    Verifique sua caixa de spam ou aguarde alguns minutos.
                  </p>
                </div>

                {/* Mostrar token apenas em desenvolvimento */}
                {resetToken && process.env.NODE_ENV === 'development' && (
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <p className="text-sm text-blue-800 mb-2">
                      <strong>Modo Desenvolvimento:</strong>
                    </p>
                    <p className="text-xs font-mono bg-white p-2 rounded border">
                      Token: {resetToken}
                    </p>
                    <Button
                      onClick={handleGoToReset}
                      size="sm"
                      className="mt-2 bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      Ir para Redefinição
                    </Button>
                  </div>
                )}

                <div className="flex flex-col gap-2">
                  <Button
                    onClick={() => setSuccess(false)}
                    variant="outline"
                    className="w-full"
                  >
                    Tentar Novamente
                  </Button>
                  
                  <Link to="/admin-login">
                    <Button variant="ghost" className="w-full">
                      Voltar ao Login
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-luxury-beige via-luxury-cream to-luxury-gold/20 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full bg-gradient-to-br from-luxury-gold/20 to-transparent"></div>
      </div>
      
      <div className="relative w-full max-w-md mx-auto z-10">
        <div className="mb-6 sm:mb-8 text-center">
          <Link to="/admin-login">
            <Button 
              variant="ghost" 
              size="sm" 
              className="mb-4 sm:mb-6 text-luxury-brown hover:text-luxury-gold hover:bg-luxury-gold/10 transition-all duration-300"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar ao Login
            </Button>
          </Link>
          
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-luxury-gold/20 rounded-full">
              <Shield className="w-8 h-8 text-luxury-brown" />
            </div>
          </div>
          
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-luxury-brown mb-2">
            Recuperar Senha
          </h1>
          <p className="text-luxury-brown/70 text-sm sm:text-base">
            Digite seu email para receber instruções
          </p>
        </div>

        <Card className="w-full shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="space-y-2 pb-4">
            <CardTitle className="text-xl sm:text-2xl text-center flex items-center justify-center text-luxury-brown">
              <Mail className="w-5 h-5 sm:w-6 sm:h-6 mr-2 text-luxury-gold" />
              Recuperação de Acesso
            </CardTitle>
            <CardDescription className="text-center text-luxury-brown/70 text-sm sm:text-base">
              Enviaremos um link para redefinir sua senha
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pt-0">
            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
              <div className="space-y-2">
                <Label 
                  htmlFor="email" 
                  className="text-luxury-brown font-medium text-sm sm:text-base"
                >
                  Email
                </Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-luxury-brown/50" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Digite seu email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10 sm:pl-12 h-11 sm:h-12 text-sm sm:text-base bg-luxury-beige/20 border-luxury-gold/30 focus:border-luxury-gold focus:ring-luxury-gold/20 rounded-xl"
                    required
                    disabled={loading}
                  />
                </div>
              </div>

              {error && (
                <Alert variant="destructive" className="border-red-200 bg-red-50">
                  <AlertDescription className="text-sm">{error}</AlertDescription>
                </Alert>
              )}

              <Button 
                type="submit" 
                className="w-full h-11 sm:h-12 text-sm sm:text-base font-semibold bg-gradient-to-r from-luxury-gold to-luxury-gold-dark hover:from-luxury-gold-dark hover:to-luxury-gold text-luxury-brown shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl"
                disabled={loading || !email.trim()}
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-luxury-brown mr-2"></div>
                    Enviando...
                  </div>
                ) : (
                  'Enviar Instruções'
                )}
              </Button>
            </form>

            <div className="mt-6 p-3 sm:p-4 bg-gradient-to-r from-luxury-gold/10 to-luxury-beige/20 rounded-xl border border-luxury-gold/20">
              <p className="text-xs sm:text-sm text-luxury-brown/80 text-center leading-relaxed">
                <strong className="text-luxury-brown">Lembre-se:</strong><br />
                O email deve estar cadastrado no sistema para receber as instruções de recuperação.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ForgotPassword; 