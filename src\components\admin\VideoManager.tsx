import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { ApiService } from '@/lib/api';
import { Plus, Edit, Trash2, Play } from 'lucide-react';

interface Video {
  id: number;
  title: string;
  description: string;
  url: string;
  thumbnail_url: string;
  category: string;
  position: number;
  active: boolean;
  created_at: string;
}

const VideoManager = () => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [editingVideo, setEditingVideo] = useState<Video | null>(null);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    url: '',
    thumbnail_url: '',
    category: 'general',
    position: 0,
    active: true
  });

  useEffect(() => {
    fetchVideos();
  }, []);

  const fetchVideos = async () => {
    try {
      const response = await ApiService.getVideos();
      console.log('Resposta da API de vídeos:', response);
      
      // Verifica se a resposta é um array ou se tem uma propriedade data
      let videosArray: Video[] = [];
      if (Array.isArray(response)) {
        videosArray = response;
      } else if (response && Array.isArray(response.data)) {
        videosArray = response.data;
      } else if (response && response.videos && Array.isArray(response.videos)) {
        videosArray = response.videos;
      } else {
        console.warn('Resposta da API não é um array válido:', response);
        videosArray = [];
      }
      
      setVideos(videosArray);
    } catch (error) {
      console.error('Erro ao buscar vídeos:', error);
      setVideos([]);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os vídeos.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      url: '',
      thumbnail_url: '',
      category: 'general',
      position: 0,
      active: true
    });
    setEditingVideo(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (editingVideo) {
        await ApiService.updateVideo(editingVideo.id, formData);
        toast({
          title: "Sucesso",
          description: "Vídeo atualizado com sucesso!",
        });
      } else {
        await ApiService.createVideo(formData);
        toast({
          title: "Sucesso",
          description: "Vídeo criado com sucesso!",
        });
      }
      
      await fetchVideos();
      resetForm();
      setIsCreateOpen(false);
    } catch (error) {
      console.error('Erro ao salvar vídeo:', error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar o vídeo.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (video: Video) => {
    setFormData({
      title: video.title,
      description: video.description,
      url: video.url,
      thumbnail_url: video.thumbnail_url,
      category: video.category,
      position: video.position,
      active: video.active
    });
    setEditingVideo(video);
    setIsCreateOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Tem certeza que deseja excluir este vídeo?')) return;
    
    try {
      await ApiService.deleteVideo(id);
      await fetchVideos();
      toast({
        title: "Sucesso",
        description: "Vídeo excluído com sucesso!",
      });
    } catch (error) {
      console.error('Erro ao excluir vídeo:', error);
      toast({
        title: "Erro",
        description: "Não foi possível excluir o vídeo.",
        variant: "destructive",
      });
    }
  };

  const handleToggleActive = async (video: Video) => {
    try {
      await ApiService.updateVideo(video.id, { ...video, active: !video.active });
      await fetchVideos();
      toast({
        title: "Sucesso",
        description: `Vídeo ${!video.active ? 'ativado' : 'desativado'} com sucesso!`,
      });
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o status do vídeo.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return <div className="animate-pulse">Carregando vídeos...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Gerenciar Vídeos</h2>
        <Dialog open={isCreateOpen} onOpenChange={(open) => {
          setIsCreateOpen(open);
          if (!open) resetForm();
        }}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Adicionar Vídeo
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">
                {editingVideo ? 'Editar Vídeo' : 'Adicionar Novo Vídeo'}
              </DialogTitle>
              <DialogDescription className="text-sm sm:text-base">
                Preencha as informações do vídeo abaixo.
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title" className="text-sm font-medium">Título *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className="h-10 sm:h-11"
                    placeholder="Digite o título do vídeo"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="category" className="text-sm font-medium">Categoria</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger className="h-10 sm:h-11">
                      <SelectValue placeholder="Selecione uma categoria" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">Geral</SelectItem>
                      <SelectItem value="tour">Tour Virtual</SelectItem>
                      <SelectItem value="construction">Construção</SelectItem>
                      <SelectItem value="testimonial">Depoimento</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium">Descrição</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="h-10 sm:h-11"
                  placeholder="Descrição opcional do vídeo"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="url" className="text-sm font-medium">URL do Vídeo *</Label>
                <Input
                  id="url"
                  type="url"
                  value={formData.url}
                  onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                  placeholder="https://youtube.com/watch?v=..."
                  className="h-10 sm:h-11"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Cole aqui a URL do YouTube, Vimeo ou outro serviço de vídeo
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="thumbnail_url" className="text-sm font-medium">URL da Thumbnail</Label>
                <Input
                  id="thumbnail_url"
                  type="url"
                  value={formData.thumbnail_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, thumbnail_url: e.target.value }))}
                  placeholder="https://exemplo.com/imagem.jpg"
                  className="h-10 sm:h-11"
                />
                <p className="text-xs text-muted-foreground">
                  URL da imagem de prévia do vídeo (opcional)
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="position" className="text-sm font-medium">Posição</Label>
                  <Input
                    id="position"
                    type="number"
                    min="0"
                    value={formData.position}
                    onChange={(e) => setFormData(prev => ({ ...prev, position: parseInt(e.target.value) || 0 }))}
                    className="h-10 sm:h-11"
                    placeholder="0"
                  />
                  <p className="text-xs text-muted-foreground">
                    Ordem de exibição (0 = primeiro)
                  </p>
                </div>

                <div className="flex items-center space-x-3 pt-6">
                  <Switch
                    id="active"
                    checked={formData.active}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, active: checked }))}
                  />
                  <Label htmlFor="active" className="text-sm font-medium">
                    Vídeo ativo
                  </Label>
                </div>
              </div>

              <div className="flex flex-col-reverse sm:flex-row justify-end space-y-2 space-y-reverse sm:space-y-0 sm:space-x-3 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateOpen(false)}
                  className="h-10 sm:h-11"
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit" 
                  className="h-10 sm:h-11 bg-luxury-gold hover:bg-luxury-gold-dark text-luxury-brown"
                >
                  {editingVideo ? 'Atualizar' : 'Criar'} Vídeo
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4 sm:gap-6">
        {videos.length === 0 ? (
          <Card className="border-dashed border-2 border-luxury-gold/30">
            <CardContent className="flex flex-col items-center justify-center h-32 sm:h-40">
              <Play className="w-8 h-8 sm:w-12 sm:h-12 text-luxury-gold/50 mb-2" />
              <p className="text-muted-foreground text-sm sm:text-base text-center">
                Nenhum vídeo encontrado
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Clique em "Adicionar Vídeo" para começar
              </p>
            </CardContent>
          </Card>
        ) : (
          videos.map((video) => (
            <Card key={video.id} className="bg-white/80 backdrop-blur-sm border-luxury-gold/20 hover:shadow-lg transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start space-y-3 sm:space-y-0">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-base sm:text-lg text-luxury-brown truncate">
                      {video.title}
                    </CardTitle>
                    <CardDescription className="text-sm mt-1">
                      {video.description || 'Sem descrição'}
                    </CardDescription>
                  </div>
                  
                  {/* Actions - Mobile */}
                  <div className="flex items-center justify-between sm:hidden">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={video.active}
                        onCheckedChange={() => handleToggleActive(video)}
                      />
                      <span className="text-xs text-muted-foreground">
                        {video.active ? 'Ativo' : 'Inativo'}
                      </span>
                    </div>
                    <div className="flex space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(video)}
                        className="h-8 w-8 p-0"
                      >
                        <Edit className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(video.id)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>

                  {/* Actions - Desktop */}
                  <div className="hidden sm:flex items-center space-x-2">
                    <Switch
                      checked={video.active}
                      onCheckedChange={() => handleToggleActive(video)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(video)}
                      className="hover:bg-luxury-gold/10"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(video.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                {/* Video Info */}
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4 text-xs sm:text-sm mb-4">
                  <div className="space-y-1">
                    <p className="font-medium text-luxury-brown/80">Categoria</p>
                    <p className="text-muted-foreground capitalize">{video.category}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="font-medium text-luxury-brown/80">Posição</p>
                    <p className="text-muted-foreground">{video.position}</p>
                  </div>
                  <div className="space-y-1 col-span-2 sm:col-span-1">
                    <p className="font-medium text-luxury-brown/80">Status</p>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${video.active ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                      <p className="text-muted-foreground">{video.active ? 'Ativo' : 'Inativo'}</p>
                    </div>
                  </div>
                </div>

                {/* Video Link */}
                <div className="flex items-center justify-between pt-3 border-t border-luxury-gold/10">
                  <a
                    href={video.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-blue-600 hover:text-blue-700 transition-colors text-sm"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Ver Vídeo
                  </a>
                  <p className="text-xs text-muted-foreground">
                    ID: {video.id}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default VideoManager; 