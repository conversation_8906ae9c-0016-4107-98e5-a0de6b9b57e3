const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { db, initDatabase } = require('./database.cjs');
const crypto = require('crypto'); // Adicionado para geração de tokens de reset
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY || 'sk_test_mock'); // Stripe para pagamentos
const QRCode = require('qrcode'); // Para gerar QR codes PIX
const multer = require('multer'); // Para upload de arquivos
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = process.env.JWT_SECRET || 'sollara-secret-key-2024';

// Middlewares
app.use(cors({
  origin: [
    'http://sollara-garden.gruposalha.com.br',
    'https://sollara-garden.gruposalha.com.br',
    'http://**************',
    'http://**************:5173',
    'http://localhost:5173',
    'http://127.0.0.1:5173'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());
app.use(express.static('public'));

// Middleware de log para debug
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  console.log('Headers:', req.headers);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Body:', req.body);
  }
  next();
});

// Rota raiz para verificação
app.get('/', (req, res) => {
  res.json({
    message: 'Sollara Garden API',
    status: 'OK',
    endpoints: [
      '/api/hero',
      '/api/gallery',
      '/api/videos',
      '/api/footer',
      '/api/auth/login',
      '/api/contact'
    ]
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Middleware de autenticação
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Token de acesso requerido' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Token inválido' });
    }
    req.user = user;
    next();
  });
};

// Rotas de autenticação
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password, email } = req.body;

    // Suporte a login por username ou email
    const identifier = email || username;

    if (!identifier || !password) {
      return res.status(400).json({ error: 'Email/username e password são obrigatórios' });
    }

    // Buscar usuário por username ou email
    let user;
    if (email) {
      user = db.prepare('SELECT * FROM users WHERE email = ?').get(email);
    } else {
      user = db.prepare('SELECT * FROM users WHERE username = ?').get(username);
    }

    if (!user || !await bcrypt.compare(password, user.password)) {
      return res.status(401).json({ error: 'Credenciais inválidas' });
    }

    // Verificar status da conta
    if (user.status !== 'active') {
      let message = '';
      switch (user.status) {
        case 'suspended':
          message = 'Conta suspensa. Entre em contato com o suporte.';
          break;
        case 'pending':
          message = 'Conta pendente de verificação. Verifique seu email.';
          break;
        default:
          message = 'Conta inativa. Entre em contato com o suporte.';
      }
      return res.status(403).json({ error: message, status: user.status });
    }

    // Verificar status da empresa (se aplicável)
    if (user.company_id) {
      const company = db.prepare('SELECT * FROM companies WHERE id = ?').get(user.company_id);

      if (!company) {
        return res.status(403).json({ error: 'Empresa não encontrada' });
      }

      if (company.status === 'suspended') {
        return res.status(403).json({
          error: 'Conta da empresa suspensa. Entre em contato com o suporte.',
          companyStatus: 'suspended'
        });
      }

      if (company.status === 'expired') {
        return res.status(403).json({
          error: 'Assinatura expirada. Renove sua assinatura para continuar.',
          companyStatus: 'expired',
          needsUpgrade: true
        });
      }

      // Verificar se ainda está em trial e se expirou
      if (company.status === 'trial' && company.trial_ends_at) {
        const now = new Date();
        const trialEnd = new Date(company.trial_ends_at);

        if (now > trialEnd) {
          // Atualizar status para expired
          db.prepare('UPDATE companies SET status = ? WHERE id = ?').run('expired', company.id);

          return res.status(403).json({
            error: 'Período de trial expirado. Faça upgrade para continuar.',
            companyStatus: 'expired',
            needsUpgrade: true
          });
        }
      }
    }

    // Atualizar último login
    db.prepare('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?').run(user.id);

    const token = jwt.sign(
      {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        company_id: user.company_id
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        company_id: user.company_id,
        status: user.status
      }
    });
  } catch (error) {
    console.error('Erro no login:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota para solicitar recuperação de senha
app.post('/api/auth/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email é obrigatório' });
    }

    const user = db.prepare('SELECT * FROM users WHERE email = ?').get(email);

    if (!user) {
      // Por segurança, não revelar se o email existe ou não
      return res.json({
        success: true,
        message: 'Se o email existir, você receberá instruções para redefinir sua senha.'
      });
    }

    // Gerar token de recuperação
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetExpires = new Date(Date.now() + 3600000); // 1 hora

    // Salvar token no banco (vamos criar uma tabela para isso)
    try {
      db.exec(`
        CREATE TABLE IF NOT EXISTS password_resets (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          token TEXT NOT NULL,
          expires_at DATETIME NOT NULL,
          used BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id)
        )
      `);

      db.prepare(`
        INSERT INTO password_resets (user_id, token, expires_at)
        VALUES (?, ?, ?)
      `).run(user.id, resetToken, resetExpires.toISOString());

      // TODO: Implementar envio de email
      console.log(`Reset token for ${email}: ${resetToken}`);

      res.json({
        success: true,
        message: 'Se o email existir, você receberá instruções para redefinir sua senha.',
        // Para desenvolvimento - remover em produção
        resetToken: process.env.NODE_ENV === 'development' ? resetToken : undefined
      });
    } catch (error) {
      console.error('Erro ao criar token de reset:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  } catch (error) {
    console.error('Erro na recuperação de senha:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota para redefinir senha
app.post('/api/auth/reset-password', async (req, res) => {
  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({ error: 'Token e nova senha são obrigatórios' });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ error: 'Nova senha deve ter pelo menos 6 caracteres' });
    }

    // Verificar se token é válido e não expirou
    const resetRecord = db.prepare(`
      SELECT pr.*, u.id as user_id, u.email
      FROM password_resets pr
      JOIN users u ON pr.user_id = u.id
      WHERE pr.token = ? AND pr.used = 0 AND pr.expires_at > datetime('now')
    `).get(token);

    if (!resetRecord) {
      return res.status(400).json({ error: 'Token inválido ou expirado' });
    }

    // Hash da nova senha
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Atualizar senha do usuário
    db.prepare('UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?')
      .run(hashedPassword, resetRecord.user_id);

    // Marcar token como usado
    db.prepare('UPDATE password_resets SET used = 1 WHERE id = ?')
      .run(resetRecord.id);

    res.json({
      success: true,
      message: 'Senha redefinida com sucesso'
    });
  } catch (error) {
    console.error('Erro ao redefinir senha:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota para verificar email
app.post('/api/auth/verify-email', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ error: 'Token de verificação é obrigatório' });
    }

    // Criar tabela de verificação de email se não existir
    try {
      db.exec(`
        CREATE TABLE IF NOT EXISTS email_verifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          email TEXT NOT NULL,
          token TEXT NOT NULL,
          expires_at DATETIME NOT NULL,
          used BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id)
        )
      `);
    } catch (error) {
      console.log('Tabela de verificação de email já existe');
    }

    // Verificar se token é válido e não expirou
    const verificationRecord = db.prepare(`
      SELECT ev.*, u.id as user_id, u.email, u.status
      FROM email_verifications ev
      JOIN users u ON ev.user_id = u.id
      WHERE ev.token = ? AND ev.used = 0 AND ev.expires_at > datetime('now')
    `).get(token);

    if (!verificationRecord) {
      return res.status(400).json({ error: 'Token inválido ou expirado' });
    }

    // Ativar usuário
    db.prepare('UPDATE users SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?')
      .run('active', verificationRecord.user_id);

    // Marcar token como usado
    db.prepare('UPDATE email_verifications SET used = 1 WHERE id = ?')
      .run(verificationRecord.id);

    res.json({
      success: true,
      message: 'Email verificado com sucesso! Sua conta foi ativada.'
    });
  } catch (error) {
    console.error('Erro ao verificar email:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota para reenviar verificação de email
app.post('/api/auth/resend-verification', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email é obrigatório' });
    }

    const user = db.prepare('SELECT * FROM users WHERE email = ? AND status = ?').get(email, 'pending');

    if (!user) {
      return res.status(400).json({
        error: 'Usuário não encontrado ou já verificado'
      });
    }

    // Gerar novo token de verificação
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 horas

    // Invalidar tokens antigos
    db.prepare('UPDATE email_verifications SET used = 1 WHERE user_id = ? AND used = 0')
      .run(user.id);

    // Criar novo token
    db.prepare(`
      INSERT INTO email_verifications (user_id, email, token, expires_at)
      VALUES (?, ?, ?, ?)
    `).run(user.id, email, verificationToken, verificationExpires.toISOString());

    // TODO: Implementar envio de email
    console.log(`Verification token for ${email}: ${verificationToken}`);

    res.json({
      success: true,
      message: 'Nova verificação enviada para seu email.',
      // Para desenvolvimento - remover em produção
      verificationToken: process.env.NODE_ENV === 'development' ? verificationToken : undefined
    });
  } catch (error) {
    console.error('Erro ao reenviar verificação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota para verificar status da conta
app.post('/api/auth/check-account-status', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email é obrigatório' });
    }

    const user = db.prepare(`
      SELECT u.*, c.status as company_status, c.trial_ends_at, c.subscription_ends_at
      FROM users u
      LEFT JOIN companies c ON u.company_id = c.id
      WHERE u.email = ?
    `).get(email);

    if (!user) {
      return res.status(404).json({ error: 'Usuário não encontrado' });
    }

    let accountStatus = {
      userStatus: user.status,
      companyStatus: user.company_status,
      canLogin: user.status === 'active',
      needsEmailVerification: user.status === 'pending'
    };

    if (user.company_id && user.company_status) {
      accountStatus.canLogin = accountStatus.canLogin &&
        (user.company_status === 'active' || user.company_status === 'trial');

      if (user.company_status === 'trial' && user.trial_ends_at) {
        const trialEnd = new Date(user.trial_ends_at);
        const now = new Date();
        accountStatus.trialDaysLeft = Math.ceil((trialEnd - now) / (1000 * 60 * 60 * 24));
        accountStatus.isTrialActive = now <= trialEnd;

        if (!accountStatus.isTrialActive) {
          accountStatus.canLogin = false;
        }
      }
    }

    res.json(accountStatus);
  } catch (error) {
    console.error('Erro ao verificar status da conta:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.post('/api/auth/verify', authenticateToken, (req, res) => {
  res.json({ valid: true, user: req.user });
});

// Rotas do formulário de contato
app.post('/api/contact', async (req, res) => {
  try {
    const { name, email, phone, message } = req.body;

    if (!name || !email || !message) {
      return res.status(400).json({ error: 'Nome, email e mensagem são obrigatórios' });
    }

    const stmt = db.prepare(`
      INSERT INTO form_submissions (name, email, phone, message)
      VALUES (?, ?, ?, ?)
    `);

    const result = stmt.run(name, email, phone || '', message);

    res.json({
      success: true,
      message: 'Mensagem enviada com sucesso!',
      id: result.lastInsertRowid
    });
  } catch (error) {
    console.error('Erro ao salvar formulário:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rotas administrativas - Submissões do formulário
app.get('/api/admin/submissions', authenticateToken, (req, res) => {
  try {
    const submissions = db.prepare(`
      SELECT * FROM form_submissions
      ORDER BY created_at DESC
    `).all();

    res.json(submissions);
  } catch (error) {
    console.error('Erro ao buscar submissões:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.put('/api/admin/submissions/:id/status', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const stmt = db.prepare(`
      UPDATE form_submissions
      SET status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const result = stmt.run(status, id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Submissão não encontrada' });
    }

    res.json({ success: true, message: 'Status atualizado com sucesso' });
  } catch (error) {
    console.error('Erro ao atualizar status:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.delete('/api/admin/submissions/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;

    const stmt = db.prepare('DELETE FROM form_submissions WHERE id = ?');
    const result = stmt.run(id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Submissão não encontrada' });
    }

    res.json({ success: true, message: 'Submissão excluída com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir submissão:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// NOVAS ROTAS PARA SISTEMA SAAS

// Rota para listar planos disponíveis
app.get('/api/plans', (req, res) => {
  try {
    const plans = db.prepare(`
      SELECT id, name, price, currency, max_campaigns, max_leads_per_month, features, is_active
      FROM plans
      WHERE is_active = 1
      ORDER BY price ASC
    `).all();

    // Parse features JSON
    const plansWithFeatures = plans.map(plan => ({
      ...plan,
      features: JSON.parse(plan.features || '[]')
    }));

    res.json(plansWithFeatures);
  } catch (error) {
    console.error('Erro ao buscar planos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota para verificar disponibilidade de domínio
app.get('/api/check-domain', (req, res) => {
  try {
    const { domain } = req.query;

    if (!domain || domain.length < 3) {
      return res.status(400).json({ error: 'Domínio inválido' });
    }

    // Verificar se domínio já está em uso
    const existingCompany = db.prepare('SELECT id FROM companies WHERE domain = ?').get(domain);

    res.json({
      available: !existingCompany,
      domain: domain
    });
  } catch (error) {
    console.error('Erro ao verificar domínio:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota para cadastro de empresa
app.post('/api/company/register', async (req, res) => {
  try {
    const {
      companyName,
      domain,
      email,
      phone,
      adminName,
      adminPassword,
      planId
    } = req.body;

    // Validações
    if (!companyName || !domain || !email || !adminName || !adminPassword || !planId) {
      return res.status(400).json({ error: 'Todos os campos obrigatórios devem ser preenchidos' });
    }

    // Verificar se domínio já existe
    const existingCompany = db.prepare('SELECT id FROM companies WHERE domain = ?').get(domain);
    if (existingCompany) {
      return res.status(400).json({ error: 'Este domínio já está em uso' });
    }

    // Verificar se email já existe
    const existingEmail = db.prepare('SELECT id FROM companies WHERE email = ?').get(email);
    if (existingEmail) {
      return res.status(400).json({ error: 'Este email já está cadastrado' });
    }

    // Verificar se plano existe
    const plan = db.prepare('SELECT * FROM plans WHERE id = ? AND is_active = 1').get(planId);
    if (!plan) {
      return res.status(400).json({ error: 'Plano selecionado inválido' });
    }

    // Calcular data de término do trial (14 dias)
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + 14);

    // Início da transação
    const transaction = db.transaction(() => {
      // Criar empresa
      const companyStmt = db.prepare(`
        INSERT INTO companies (name, domain, email, phone, plan_id, status, trial_ends_at)
        VALUES (?, ?, ?, ?, ?, 'trial', ?)
      `);

      const companyResult = companyStmt.run(
        companyName,
        domain,
        email,
        phone || '',
        planId,
        trialEndDate.toISOString()
      );

      const companyId = companyResult.lastInsertRowid;

      // Criar usuário admin da empresa (status pending para verificação de email)
      const hashedPassword = bcrypt.hashSync(adminPassword, 10);
      const userStmt = db.prepare(`
        INSERT INTO users (username, password, email, role, company_id, status)
        VALUES (?, ?, ?, 'cliente_admin', ?, 'pending')
      `);

      const username = domain + '_admin'; // username único baseado no domínio
      const userResult = userStmt.run(username, hashedPassword, email, companyId);
      const userId = userResult.lastInsertRowid;

      // Gerar token de verificação de email
      const verificationToken = crypto.randomBytes(32).toString('hex');
      const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 horas

      // Criar registro de verificação de email
      const verificationStmt = db.prepare(`
        INSERT INTO email_verifications (user_id, email, token, expires_at)
        VALUES (?, ?, ?, ?)
      `);
      verificationStmt.run(userId, email, verificationToken, verificationExpires.toISOString());

      // Criar configurações de personalização padrão
      const customizationStmt = db.prepare(`
        INSERT INTO company_customizations (company_id)
        VALUES (?)
      `);
      customizationStmt.run(companyId);

      return { companyId, username, verificationToken };
    });

    const result = transaction();

    // TODO: Implementar envio de email de verificação
    console.log(`Verification token for ${email}: ${result.verificationToken}`);

    res.json({
      success: true,
      message: 'Empresa cadastrada com sucesso! Verifique seu email para ativar sua conta.',
      company: {
        id: result.companyId,
        name: companyName,
        domain: domain,
        username: result.username,
        trialEnds: trialEndDate.toISOString(),
        plan: plan.name
      },
      emailVerificationRequired: true,
      // Para desenvolvimento - remover em produção
      verificationToken: process.env.NODE_ENV === 'development' ? result.verificationToken : undefined
    });

  } catch (error) {
    console.error('Erro ao cadastrar empresa:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// ROTAS SUPER ADMIN

// Middleware para verificar se é super admin
const requireSuperAdmin = (req, res, next) => {
  if (req.user.role !== 'super_admin') {
    return res.status(403).json({ error: 'Acesso negado. Super admin requerido.' });
  }
  next();
};

// Estatísticas do dashboard super admin
app.get('/api/super-admin/stats', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    // Total de empresas
    const totalCompanies = db.prepare('SELECT COUNT(*) as count FROM companies').get().count;

    // Empresas ativas
    const activeCompanies = db.prepare('SELECT COUNT(*) as count FROM companies WHERE status = "active"').get().count;

    // Empresas em trial
    const trialCompanies = db.prepare('SELECT COUNT(*) as count FROM companies WHERE status = "trial"').get().count;

    // Receita mensal (estimativa baseada em empresas ativas)
    const monthlyRevenue = db.prepare(`
      SELECT SUM(p.price) as revenue
      FROM companies c
      JOIN plans p ON c.plan_id = p.id
      WHERE c.status = 'active'
    `).get().revenue || 0;

    // Total de leads capturados
    const totalLeads = db.prepare('SELECT COUNT(*) as count FROM form_submissions').get().count;

    // Novos cadastros neste mês
    const firstDayOfMonth = new Date();
    firstDayOfMonth.setDate(1);
    firstDayOfMonth.setHours(0, 0, 0, 0);

    const newSignupsThisMonth = db.prepare(`
      SELECT COUNT(*) as count
      FROM companies
      WHERE created_at >= ?
    `).get(firstDayOfMonth.toISOString()).count;

    res.json({
      totalCompanies,
      activeCompanies,
      trialCompanies,
      monthlyRevenue,
      totalLeads,
      newSignupsThisMonth
    });

  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar empresas para super admin
app.get('/api/super-admin/companies', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const companies = db.prepare(`
      SELECT
        c.*,
        p.name as plan_name,
        p.price as plan_price
      FROM companies c
      JOIN plans p ON c.plan_id = p.id
      ORDER BY c.created_at DESC
    `).all();

    res.json(companies);
  } catch (error) {
    console.error('Erro ao buscar empresas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Ações em empresas (ativar/suspender)
app.post('/api/super-admin/companies/:id/:action', authenticateToken, requireSuperAdmin, (req, res) => {
  try {
    const { id, action } = req.params;

    let newStatus;
    switch (action) {
      case 'activate':
        newStatus = 'active';
        break;
      case 'suspend':
        newStatus = 'suspended';
        break;
      case 'delete':
        // Para exclusão, seria necessário mais validações
        return res.status(400).json({ error: 'Exclusão não implementada por segurança' });
      default:
        return res.status(400).json({ error: 'Ação inválida' });
    }

    const stmt = db.prepare('UPDATE companies SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?');
    const result = stmt.run(newStatus, id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Empresa não encontrada' });
    }

    res.json({
      success: true,
      message: `Empresa ${action === 'activate' ? 'ativada' : 'suspensa'} com sucesso`
    });

  } catch (error) {
    console.error('Erro ao atualizar empresa:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rotas administrativas - Vídeos
app.get('/api/admin/videos', authenticateToken, (req, res) => {
  try {
    const videos = db.prepare(`
      SELECT id, title, description, url as video_url,
             CASE WHEN url LIKE '%youtube%' OR url LIKE '%youtu.be%' THEN 'youtube' ELSE 'file' END as video_type,
             thumbnail_url, active as is_active, position as order_index,
             created_at, updated_at
      FROM videos
      ORDER BY position ASC, created_at DESC
    `).all();

    res.json({ success: true, data: videos });
  } catch (error) {
    console.error('Erro ao buscar vídeos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.get('/api/videos', (req, res) => {
  try {
    const videos = db.prepare(`
      SELECT id, title, description, url as video_url,
             CASE WHEN url LIKE '%youtube%' OR url LIKE '%youtu.be%' THEN 'youtube' ELSE 'file' END as video_type,
             thumbnail_url, active as is_active, position as order_index,
             created_at, updated_at
      FROM videos
      WHERE active = 1
      ORDER BY position ASC, created_at DESC
    `).all();

    res.json({ success: true, data: videos });
  } catch (error) {
    console.error('Erro ao buscar vídeos públicos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.post('/api/admin/videos', authenticateToken, (req, res) => {
  try {
    const { title, description, url, thumbnail_url, category, position } = req.body;

    if (!title || !url) {
      return res.status(400).json({ error: 'Título e URL são obrigatórios' });
    }

    const stmt = db.prepare(`
      INSERT INTO videos (title, description, url, thumbnail_url, category, position)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      title,
      description || '',
      url,
      thumbnail_url || '',
      category || 'general',
      position || 0
    );

    res.json({
      success: true,
      message: 'Vídeo adicionado com sucesso!',
      id: result.lastInsertRowid
    });
  } catch (error) {
    console.error('Erro ao adicionar vídeo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.put('/api/admin/videos/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, url, thumbnail_url, category, position, active } = req.body;

    const stmt = db.prepare(`
      UPDATE videos
      SET title = ?, description = ?, url = ?, thumbnail_url = ?,
          category = ?, position = ?, active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const result = stmt.run(
      title, description || '', url, thumbnail_url || '',
      category || 'general', position || 0, active !== undefined ? active : 1, id
    );

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Vídeo não encontrado' });
    }

    res.json({ success: true, message: 'Vídeo atualizado com sucesso' });
  } catch (error) {
    console.error('Erro ao atualizar vídeo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.delete('/api/admin/videos/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;

    const stmt = db.prepare('DELETE FROM videos WHERE id = ?');
    const result = stmt.run(id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Vídeo não encontrado' });
    }

    res.json({ success: true, message: 'Vídeo excluído com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir vídeo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rotas administrativas - Configurações do site
app.get('/api/admin/settings', authenticateToken, (req, res) => {
  try {
    const settings = db.prepare('SELECT * FROM site_settings ORDER BY key').all();

    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = setting.value;
    });

    res.json(settingsObj);
  } catch (error) {
    console.error('Erro ao buscar configurações:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.put('/api/admin/settings', authenticateToken, (req, res) => {
  try {
    const settings = req.body;

    const stmt = db.prepare(`
      INSERT OR REPLACE INTO site_settings (key, value, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `);

    Object.entries(settings).forEach(([key, value]) => {
      stmt.run(key, value);
    });

    res.json({ success: true, message: 'Configurações atualizadas com sucesso' });
  } catch (error) {
    console.error('Erro ao atualizar configurações:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rota para buscar configurações públicas
app.get('/api/settings', (req, res) => {
  try {
    const settings = db.prepare('SELECT * FROM site_settings').all();

    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = setting.value;
    });

    res.json(settingsObj);
  } catch (error) {
    console.error('Erro ao buscar configurações públicas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rotas administrativas - Galeria de imagens
app.get('/api/admin/gallery', authenticateToken, (req, res) => {
  try {
    const images = db.prepare(`
      SELECT id, title, description, image_url, order_index, is_active,
             created_at, updated_at
      FROM gallery_images
      ORDER BY order_index ASC, created_at DESC
    `).all();

    res.json({ success: true, data: images });
  } catch (error) {
    console.error('Erro ao buscar imagens da galeria:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.get('/api/gallery', (req, res) => {
  try {
    const images = db.prepare(`
      SELECT id, title, description, image_url, order_index
      FROM gallery_images
      WHERE is_active = 1
      ORDER BY order_index ASC, created_at DESC
    `).all();

    res.json({ success: true, data: images });
  } catch (error) {
    console.error('Erro ao buscar imagens públicas da galeria:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.post('/api/admin/gallery', authenticateToken, (req, res) => {
  try {
    const { title, description, image_url, order_index } = req.body;

    if (!title || !image_url) {
      return res.status(400).json({ error: 'Título e URL da imagem são obrigatórios' });
    }

    const stmt = db.prepare(`
      INSERT INTO gallery_images (title, description, image_url, order_index, is_active)
      VALUES (?, ?, ?, ?, 1)
    `);

    const result = stmt.run(title, description || '', image_url, order_index || 0);

    res.json({
      success: true,
      message: 'Imagem adicionada à galeria com sucesso!',
      data: { id: result.lastInsertRowid }
    });
  } catch (error) {
    console.error('Erro ao adicionar imagem à galeria:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.put('/api/admin/gallery/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, image_url, order_index, is_active } = req.body;

    const stmt = db.prepare(`
      UPDATE gallery_images
      SET title = ?, description = ?, image_url = ?, order_index = ?,
          is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const result = stmt.run(
      title, description || '', image_url, order_index || 0,
      is_active !== undefined ? is_active : 1, id
    );

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Imagem não encontrada' });
    }

    res.json({ success: true, message: 'Imagem atualizada com sucesso' });
  } catch (error) {
    console.error('Erro ao atualizar imagem:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.delete('/api/admin/gallery/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;

    const stmt = db.prepare('DELETE FROM gallery_images WHERE id = ?');
    const result = stmt.run(id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Imagem não encontrada' });
    }

    res.json({ success: true, message: 'Imagem excluída da galeria com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir imagem:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rotas administrativas - Conteúdo hero
app.get('/api/admin/hero', authenticateToken, (req, res) => {
  try {
    const heroData = db.prepare(`
      SELECT * FROM content_management
      WHERE section = 'hero'
      ORDER BY type
    `).all();

    const hero = {};
    heroData.forEach(item => {
      hero[item.type] = item.content;
    });

    res.json({ success: true, data: hero });
  } catch (error) {
    console.error('Erro ao buscar conteúdo do hero:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.put('/api/admin/hero', authenticateToken, (req, res) => {
  try {
    const heroData = req.body;

    const stmt = db.prepare(`
      INSERT OR REPLACE INTO content_management (section, type, content, position, active)
      VALUES ('hero', ?, ?, 0, 1)
    `);

    Object.entries(heroData).forEach(([fieldName, fieldValue]) => {
      stmt.run(fieldName, fieldValue);
    });

    res.json({ success: true, message: 'Conteúdo do hero atualizado com sucesso' });
  } catch (error) {
    console.error('Erro ao atualizar hero:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.get('/api/hero', (req, res) => {
  try {
    const heroData = db.prepare(`
      SELECT * FROM content_management
      WHERE section = 'hero'
      ORDER BY type
    `).all();

    const hero = {};
    heroData.forEach(item => {
      hero[item.type] = item.content;
    });

    res.json({ success: true, data: hero });
  } catch (error) {
    console.error('Erro ao buscar conteúdo público do hero:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rotas administrativas - Conteúdo do rodapé
app.get('/api/admin/footer', authenticateToken, (req, res) => {
  try {
    const footerData = db.prepare(`
      SELECT * FROM content_management
      WHERE section = 'footer'
      ORDER BY type
    `).all();

    const footer = {};
    footerData.forEach(item => {
      footer[item.type] = item.content;
    });

    res.json({ success: true, data: footer });
  } catch (error) {
    console.error('Erro ao buscar conteúdo do rodapé:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.put('/api/admin/footer', authenticateToken, (req, res) => {
  try {
    const footerData = req.body;

    const stmt = db.prepare(`
      INSERT OR REPLACE INTO content_management (section, type, content, position, active)
      VALUES ('footer', ?, ?, 0, 1)
    `);

    Object.entries(footerData).forEach(([fieldName, fieldValue]) => {
      stmt.run(fieldName, fieldValue);
    });

    res.json({ success: true, message: 'Conteúdo do rodapé atualizado com sucesso' });
  } catch (error) {
    console.error('Erro ao atualizar rodapé:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

app.get('/api/footer', (req, res) => {
  try {
    const footerData = db.prepare(`
      SELECT * FROM content_management
      WHERE section = 'footer'
      ORDER BY type
    `).all();

    const footer = {};
    footerData.forEach(item => {
      footer[item.type] = item.content;
    });

    res.json({ success: true, data: footer });
  } catch (error) {
    console.error('Erro ao buscar conteúdo público do rodapé:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// ===== ROTAS DE PAGAMENTOS =====

// Buscar planos disponíveis
app.get('/api/plans', (req, res) => {
  try {
    const plans = db.prepare('SELECT * FROM plans WHERE is_active = 1 ORDER BY price').all();
    res.json({ success: true, data: plans });
  } catch (error) {
    console.error('Erro ao buscar planos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar sessão de pagamento Stripe
app.post('/api/payments/stripe/create-session', authenticateToken, async (req, res) => {
  try {
    const { plan_id, success_url, cancel_url } = req.body;
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    // Buscar plano
    const plan = db.prepare('SELECT * FROM plans WHERE id = ?').get(plan_id);
    if (!plan) {
      return res.status(404).json({ error: 'Plano não encontrado' });
    }

    // Buscar empresa
    const company = db.prepare('SELECT * FROM companies WHERE id = ?').get(user.company_id);
    if (!company) {
      return res.status(404).json({ error: 'Empresa não encontrada' });
    }

    // Criar ou recuperar customer no Stripe
    let customerId = company.stripe_customer_id;
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: company.email,
        name: company.name,
        metadata: {
          company_id: company.id.toString()
        }
      });
      customerId = customer.id;

      // Salvar customer ID no banco
      db.prepare('UPDATE companies SET stripe_customer_id = ? WHERE id = ?')
        .run(customerId, company.id);
    }

    // Criar sessão de checkout
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [{
        price_data: {
          currency: 'brl',
          product_data: {
            name: `Plano ${plan.name} - CaptaFlow`,
            description: `Assinatura mensal do plano ${plan.name}`
          },
          unit_amount: Math.round(plan.price * 100), // Converter para centavos
          recurring: {
            interval: 'month'
          }
        },
        quantity: 1,
      }],
      mode: 'subscription',
      success_url: success_url || `${req.headers.origin}/admin?payment=success`,
      cancel_url: cancel_url || `${req.headers.origin}/admin?payment=cancelled`,
      metadata: {
        company_id: company.id.toString(),
        plan_id: plan.id.toString()
      }
    });

    // Registrar transação no banco
    db.prepare(`
      INSERT INTO transactions (company_id, plan_id, amount, payment_method, stripe_payment_intent_id, status)
      VALUES (?, ?, ?, 'stripe', ?, 'pending')
    `).run(company.id, plan.id, plan.price, session.id);

    res.json({
      success: true,
      checkout_url: session.url,
      session_id: session.id
    });

  } catch (error) {
    console.error('Erro ao criar sessão Stripe:', error);
    res.status(500).json({ error: 'Erro ao processar pagamento' });
  }
});

// Webhook do Stripe para processar eventos de pagamento
app.post('/api/payments/stripe/webhook', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Processar diferentes tipos de eventos
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object;
      handleSuccessfulPayment(session);
      break;

    case 'invoice.payment_succeeded':
      const invoice = event.data.object;
      handleSuccessfulRecurringPayment(invoice);
      break;

    case 'invoice.payment_failed':
      const failedInvoice = event.data.object;
      handleFailedPayment(failedInvoice);
      break;

    default:
      console.log(`Evento não tratado: ${event.type}`);
  }

  res.json({received: true});
});

// Função para processar pagamento bem-sucedido
function handleSuccessfulPayment(session) {
  try {
    const companyId = parseInt(session.metadata.company_id);
    const planId = parseInt(session.metadata.plan_id);

    // Atualizar status da transação
    db.prepare(`
      UPDATE transactions
      SET status = 'paid', paid_at = CURRENT_TIMESTAMP
      WHERE stripe_payment_intent_id = ? AND company_id = ?
    `).run(session.id, companyId);

    // Atualizar empresa para ativa e definir data de renovação
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    db.prepare(`
      UPDATE companies
      SET status = 'active', plan_id = ?, subscription_ends_at = ?
      WHERE id = ?
    `).run(planId, nextMonth.toISOString(), companyId);

    console.log(`Pagamento processado com sucesso para empresa ${companyId}`);
  } catch (error) {
    console.error('Erro ao processar pagamento bem-sucedido:', error);
  }
}

// Função para processar pagamento recorrente bem-sucedido
function handleSuccessfulRecurringPayment(invoice) {
  try {
    const customerId = invoice.customer;

    // Buscar empresa pelo customer ID
    const company = db.prepare('SELECT * FROM companies WHERE stripe_customer_id = ?').get(customerId);
    if (!company) {
      console.error('Empresa não encontrada para customer:', customerId);
      return;
    }

    // Estender assinatura por mais um mês
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    db.prepare(`
      UPDATE companies
      SET status = 'active', subscription_ends_at = ?
      WHERE id = ?
    `).run(nextMonth.toISOString(), company.id);

    // Registrar transação recorrente
    db.prepare(`
      INSERT INTO transactions (company_id, plan_id, amount, payment_method, stripe_payment_intent_id, status, paid_at)
      VALUES (?, ?, ?, 'stripe', ?, 'paid', CURRENT_TIMESTAMP)
    `).run(company.id, company.plan_id, invoice.amount_paid / 100, invoice.id);

    console.log(`Pagamento recorrente processado para empresa ${company.id}`);
  } catch (error) {
    console.error('Erro ao processar pagamento recorrente:', error);
  }
}

// Função para processar falha de pagamento
function handleFailedPayment(invoice) {
  try {
    const customerId = invoice.customer;

    // Buscar empresa pelo customer ID
    const company = db.prepare('SELECT * FROM companies WHERE stripe_customer_id = ?').get(customerId);
    if (!company) {
      console.error('Empresa não encontrada para customer:', customerId);
      return;
    }

    // Marcar como atrasado, mas não suspender imediatamente
    db.prepare(`
      UPDATE companies
      SET status = 'overdue'
      WHERE id = ?
    `).run(company.id);

    console.log(`Pagamento falhado para empresa ${company.id} - marcada como overdue`);
  } catch (error) {
    console.error('Erro ao processar falha de pagamento:', error);
  }
}

// Gerar pagamento PIX
app.post('/api/payments/pix/generate', authenticateToken, async (req, res) => {
  try {
    const { plan_id } = req.body;
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    // Buscar plano
    const plan = db.prepare('SELECT * FROM plans WHERE id = ?').get(plan_id);
    if (!plan) {
      return res.status(404).json({ error: 'Plano não encontrado' });
    }

    // Buscar empresa
    const company = db.prepare('SELECT * FROM companies WHERE id = ?').get(user.company_id);
    if (!company) {
      return res.status(404).json({ error: 'Empresa não encontrada' });
    }

    const pixKey = '53882441000120'; // Chave PIX da CaptaFlow
    const amount = plan.price;
    const description = `Plano ${plan.name} - ${company.name}`;

    // Gerar código PIX (formato simples para demonstração)
    const pixCode = `00020126580014br.gov.bcb.pix0136${pixKey}5204000053039865802BR5913CaptaFlow6009SAO PAULO62070503***6304${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    // Gerar QR Code
    const qrCodeData = await QRCode.toDataURL(pixCode);

    // Registrar transação PIX no banco
    const result = db.prepare(`
      INSERT INTO transactions (company_id, plan_id, amount, payment_method, pix_key, pix_qr_code, status)
      VALUES (?, ?, ?, 'pix', ?, ?, 'pending')
    `).run(company.id, plan.id, amount, pixKey, qrCodeData);

    res.json({
      success: true,
      data: {
        transaction_id: result.lastInsertRowid,
        pix_code: pixCode,
        qr_code: qrCodeData,
        amount: amount,
        pix_key: pixKey,
        description: description
      }
    });

  } catch (error) {
    console.error('Erro ao gerar PIX:', error);
    res.status(500).json({ error: 'Erro ao gerar pagamento PIX' });
  }
});

// Upload de comprovante PIX
app.post('/api/payments/pix/upload-receipt', authenticateToken, async (req, res) => {
  try {
    const { transaction_id, receipt_url } = req.body;
    const user = req.user;

    if (!transaction_id || !receipt_url) {
      return res.status(400).json({ error: 'ID da transação e URL do comprovante são obrigatórios' });
    }

    // Verificar se a transação pertence à empresa do usuário
    const transaction = db.prepare(`
      SELECT * FROM transactions
      WHERE id = ? AND company_id = ? AND payment_method = 'pix'
    `).get(transaction_id, user.company_id);

    if (!transaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    // Atualizar transação com o comprovante
    db.prepare(`
      UPDATE transactions
      SET pix_receipt_url = ?, status = 'pending_approval'
      WHERE id = ?
    `).run(receipt_url, transaction_id);

    res.json({
      success: true,
      message: 'Comprovante enviado com sucesso. Aguarde aprovação do pagamento.'
    });

  } catch (error) {
    console.error('Erro ao fazer upload do comprovante:', error);
    res.status(500).json({ error: 'Erro ao processar comprovante' });
  }
});

// Listar transações da empresa
app.get('/api/payments/transactions', authenticateToken, (req, res) => {
  try {
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    const transactions = db.prepare(`
      SELECT t.*, p.name as plan_name
      FROM transactions t
      LEFT JOIN plans p ON t.plan_id = p.id
      WHERE t.company_id = ?
      ORDER BY t.created_at DESC
    `).all(user.company_id);

    res.json({ success: true, data: transactions });
  } catch (error) {
    console.error('Erro ao buscar transações:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Aprovar pagamento PIX (Super Admin)
app.post('/api/admin/payments/pix/approve', authenticateToken, (req, res) => {
  try {
    const { transaction_id } = req.body;
    const user = req.user;

    // Verificar se é super admin
    if (user.role !== 'super_admin') {
      return res.status(403).json({ error: 'Acesso negado' });
    }

    // Buscar transação
    const transaction = db.prepare(`
      SELECT t.*, c.id as company_id, c.plan_id as current_plan_id
      FROM transactions t
      JOIN companies c ON t.company_id = c.id
      WHERE t.id = ? AND t.payment_method = 'pix' AND t.status = 'pending_approval'
    `).get(transaction_id);

    if (!transaction) {
      return res.status(404).json({ error: 'Transação não encontrada ou não pendente' });
    }

    // Aprovar pagamento
    db.prepare(`
      UPDATE transactions
      SET status = 'paid', paid_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(transaction_id);

    // Ativar empresa e definir data de renovação
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    db.prepare(`
      UPDATE companies
      SET status = 'active', plan_id = ?, subscription_ends_at = ?
      WHERE id = ?
    `).run(transaction.plan_id, nextMonth.toISOString(), transaction.company_id);

    res.json({
      success: true,
      message: 'Pagamento PIX aprovado com sucesso'
    });

  } catch (error) {
    console.error('Erro ao aprovar pagamento PIX:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Rejeitar pagamento PIX (Super Admin)
app.post('/api/admin/payments/pix/reject', authenticateToken, (req, res) => {
  try {
    const { transaction_id, reason } = req.body;
    const user = req.user;

    // Verificar se é super admin
    if (user.role !== 'super_admin') {
      return res.status(403).json({ error: 'Acesso negado' });
    }

    // Rejeitar pagamento
    db.prepare(`
      UPDATE transactions
      SET status = 'rejected'
      WHERE id = ? AND payment_method = 'pix' AND status = 'pending_approval'
    `).run(transaction_id);

    res.json({
      success: true,
      message: 'Pagamento PIX rejeitado'
    });

  } catch (error) {
    console.error('Erro ao rejeitar pagamento PIX:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// ===== ROTAS ADMINISTRATIVAS PARA GESTÃO DE PAGAMENTOS =====

// Listar todos os pagamentos pendentes (Super Admin)
app.get('/api/admin/payments/pending', authenticateToken, (req, res) => {
  try {
    const user = req.user;

    if (user.role !== 'super_admin') {
      return res.status(403).json({ error: 'Acesso negado' });
    }

    const pendingPayments = db.prepare(`
      SELECT
        t.*,
        c.name as company_name,
        c.email as company_email,
        p.name as plan_name
      FROM transactions t
      JOIN companies c ON t.company_id = c.id
      JOIN plans p ON t.plan_id = p.id
      WHERE t.status IN ('pending_approval', 'pending')
      ORDER BY t.created_at ASC
    `).all();

    res.json({ success: true, data: pendingPayments });
  } catch (error) {
    console.error('Erro ao buscar pagamentos pendentes:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Dashboard financeiro (Super Admin)
app.get('/api/admin/financial-dashboard', authenticateToken, (req, res) => {
  try {
    const user = req.user;

    if (user.role !== 'super_admin') {
      return res.status(403).json({ error: 'Acesso negado' });
    }

    // Receita total
    const totalRevenue = db.prepare(`
      SELECT COALESCE(SUM(amount), 0) as total
      FROM transactions
      WHERE status = 'paid'
    `).get();

    // Receita do mês atual
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
    const monthlyRevenue = db.prepare(`
      SELECT COALESCE(SUM(amount), 0) as total
      FROM transactions
      WHERE status = 'paid' AND DATE(paid_at) LIKE ?
    `).get(`${currentMonth}%`);

    // Clientes ativos por plano
    const clientsByPlan = db.prepare(`
      SELECT p.name, COUNT(c.id) as count
      FROM plans p
      LEFT JOIN companies c ON c.plan_id = p.id AND c.status = 'active'
      GROUP BY p.id, p.name
      ORDER BY p.price
    `).all();

    // Pagamentos pendentes
    const pendingPayments = db.prepare(`
      SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as total_amount
      FROM transactions
      WHERE status IN ('pending', 'pending_approval')
    `).get();

    // Transações recentes
    const recentTransactions = db.prepare(`
      SELECT
        t.*,
        c.name as company_name,
        p.name as plan_name
      FROM transactions t
      JOIN companies c ON t.company_id = c.id
      JOIN plans p ON t.plan_id = p.id
      ORDER BY t.created_at DESC
      LIMIT 10
    `).all();

    res.json({
      success: true,
      data: {
        totalRevenue: totalRevenue.total,
        monthlyRevenue: monthlyRevenue.total,
        clientsByPlan,
        pendingPayments,
        recentTransactions
      }
    });

  } catch (error) {
    console.error('Erro ao buscar dashboard financeiro:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// ===== ROTAS DE GESTÃO DE PLANOS =====

// Buscar plano atual da empresa
app.get('/api/company/current-plan', authenticateToken, (req, res) => {
  try {
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    const companyPlan = db.prepare(`
      SELECT
        c.*,
        p.name as plan_name,
        p.price as plan_price,
        p.max_campaigns,
        p.max_leads_per_month,
        p.features
      FROM companies c
      JOIN plans p ON c.plan_id = p.id
      WHERE c.id = ?
    `).get(user.company_id);

    if (!companyPlan) {
      return res.status(404).json({ error: 'Empresa não encontrada' });
    }

    // Calcular dias restantes do trial/assinatura
    let daysRemaining = null;
    if (companyPlan.status === 'trial' && companyPlan.trial_ends_at) {
      const trialEnd = new Date(companyPlan.trial_ends_at);
      const now = new Date();
      daysRemaining = Math.ceil((trialEnd - now) / (1000 * 60 * 60 * 24));
    } else if (companyPlan.subscription_ends_at) {
      const subscriptionEnd = new Date(companyPlan.subscription_ends_at);
      const now = new Date();
      daysRemaining = Math.ceil((subscriptionEnd - now) / (1000 * 60 * 60 * 24));
    }

    res.json({
      success: true,
      data: {
        ...companyPlan,
        features: JSON.parse(companyPlan.features || '[]'),
        days_remaining: daysRemaining
      }
    });

  } catch (error) {
    console.error('Erro ao buscar plano atual:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Solicitar mudança de plano
app.post('/api/company/change-plan', authenticateToken, async (req, res) => {
  try {
    const { new_plan_id, payment_method } = req.body;
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    // Buscar empresa atual
    const company = db.prepare('SELECT * FROM companies WHERE id = ?').get(user.company_id);
    if (!company) {
      return res.status(404).json({ error: 'Empresa não encontrada' });
    }

    // Buscar plano atual e novo plano
    const currentPlan = db.prepare('SELECT * FROM plans WHERE id = ?').get(company.plan_id);
    const newPlan = db.prepare('SELECT * FROM plans WHERE id = ?').get(new_plan_id);

    if (!newPlan) {
      return res.status(404).json({ error: 'Novo plano não encontrado' });
    }

    // Verificar se é upgrade ou downgrade
    const isUpgrade = newPlan.price > currentPlan.price;

    // Calcular valor proporcional (se aplicável)
    let amount = newPlan.price;
    let description = `Mudança para plano ${newPlan.name}`;

    if (company.status === 'active' && company.subscription_ends_at) {
      // Calcular proporção do mês
      const now = new Date();
      const subscriptionEnd = new Date(company.subscription_ends_at);
      const daysInMonth = 30; // Simplificado para demonstração
      const daysRemaining = Math.ceil((subscriptionEnd - now) / (1000 * 60 * 60 * 24));

      if (isUpgrade) {
        // Upgrade: cobrar diferença proporcional
        const priceDifference = newPlan.price - currentPlan.price;
        amount = (priceDifference * daysRemaining) / daysInMonth;
        description = `Upgrade para plano ${newPlan.name} (${daysRemaining} dias restantes)`;
      } else {
        // Downgrade: aplicar crédito no próximo ciclo
        amount = newPlan.price;
        description = `Downgrade para plano ${newPlan.name} (efetivo no próximo ciclo)`;
      }
    }

    // Se for payment_method = 'stripe', criar sessão
    if (payment_method === 'stripe') {
      // Criar customer se não existir
      let customerId = company.stripe_customer_id;
      if (!customerId) {
        const customer = await stripe.customers.create({
          email: company.email,
          name: company.name,
          metadata: { company_id: company.id.toString() }
        });
        customerId = customer.id;
        db.prepare('UPDATE companies SET stripe_customer_id = ? WHERE id = ?')
          .run(customerId, company.id);
      }

      // Criar sessão de checkout
      const session = await stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [{
          price_data: {
            currency: 'brl',
            product_data: {
              name: description,
              description: `Mudança de plano - CaptaFlow`
            },
            unit_amount: Math.round(amount * 100),
          },
          quantity: 1,
        }],
        mode: 'payment',
        success_url: `${req.headers.origin}/admin?plan_change=success`,
        cancel_url: `${req.headers.origin}/admin?plan_change=cancelled`,
        metadata: {
          company_id: company.id.toString(),
          new_plan_id: new_plan_id.toString(),
          change_type: isUpgrade ? 'upgrade' : 'downgrade'
        }
      });

      // Registrar transação
      db.prepare(`
        INSERT INTO transactions (company_id, plan_id, amount, payment_method, stripe_payment_intent_id, status)
        VALUES (?, ?, ?, 'stripe', ?, 'pending')
      `).run(company.id, new_plan_id, amount, session.id);

      res.json({
        success: true,
        payment_required: true,
        checkout_url: session.url,
        amount: amount
      });

    } else if (payment_method === 'pix') {
      // Gerar PIX para mudança de plano
      const pixKey = '53882441000120';
      const pixCode = `00020126580014br.gov.bcb.pix0136${pixKey}5204000053039865802BR5913CaptaFlow6009SAO PAULO62070503***6304${Math.random().toString(36).substr(2, 4).toUpperCase()}`;
      const qrCodeData = await QRCode.toDataURL(pixCode);

      // Registrar transação PIX
      const result = db.prepare(`
        INSERT INTO transactions (company_id, plan_id, amount, payment_method, pix_key, pix_qr_code, status)
        VALUES (?, ?, ?, 'pix', ?, ?, 'pending')
      `).run(company.id, new_plan_id, amount, pixKey, qrCodeData);

      res.json({
        success: true,
        payment_required: true,
        pix_data: {
          transaction_id: result.lastInsertRowid,
          pix_code: pixCode,
          qr_code: qrCodeData,
          amount: amount,
          pix_key: pixKey,
          description: description
        }
      });

    } else {
      // Mudança direta (para downgrades ou casos especiais)
      if (!isUpgrade || amount <= 0) {
        // Aplicar mudança imediatamente
        db.prepare('UPDATE companies SET plan_id = ? WHERE id = ?')
          .run(new_plan_id, company.id);

        // Registrar transação como cortesia (se aplicável)
        if (amount <= 0) {
          db.prepare(`
            INSERT INTO transactions (company_id, plan_id, amount, payment_method, status, paid_at)
            VALUES (?, ?, 0, 'free', 'paid', CURRENT_TIMESTAMP)
          `).run(company.id, new_plan_id);
        }

        res.json({
          success: true,
          payment_required: false,
          message: 'Plano alterado com sucesso'
        });
      } else {
        res.status(400).json({ error: 'Método de pagamento é obrigatório para upgrades' });
      }
    }

  } catch (error) {
    console.error('Erro ao mudar plano:', error);
    res.status(500).json({ error: 'Erro ao processar mudança de plano' });
  }
});

// Cancelar assinatura
app.post('/api/company/cancel-subscription', authenticateToken, (req, res) => {
  try {
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    // Buscar empresa
    const company = db.prepare('SELECT * FROM companies WHERE id = ?').get(user.company_id);
    if (!company) {
      return res.status(404).json({ error: 'Empresa não encontrada' });
    }

    // Marcar para cancelamento no final do período
    db.prepare(`
      UPDATE companies
      SET status = 'cancelled'
      WHERE id = ?
    `).run(user.company_id);

    // TODO: Cancelar assinatura no Stripe se existir

    res.json({
      success: true,
      message: 'Assinatura cancelada. Sua conta permanecerá ativa até o final do período pago.'
    });

  } catch (error) {
    console.error('Erro ao cancelar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Histórico de faturas
app.get('/api/company/billing-history', authenticateToken, (req, res) => {
  try {
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    const billingHistory = db.prepare(`
      SELECT
        t.*,
        p.name as plan_name
      FROM transactions t
      JOIN plans p ON t.plan_id = p.id
      WHERE t.company_id = ? AND t.status = 'paid'
      ORDER BY t.paid_at DESC
    `).all(user.company_id);

    res.json({ success: true, data: billingHistory });

  } catch (error) {
    console.error('Erro ao buscar histórico de faturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// ===== APIS DE PERSONALIZAÇÃO WHITELABEL =====

// Buscar configurações de personalização da empresa
app.get('/api/company/customization', authenticateToken, (req, res) => {
  try {
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    const customization = db.prepare(`
      SELECT * FROM company_customizations
      WHERE company_id = ?
    `).get(user.company_id);

    if (!customization) {
      // Criar configuração padrão se não existir
      const defaultCustomization = {
        company_id: user.company_id,
        primary_color: '#1e40af',
        secondary_color: '#f59e0b',
        background_color: '#ffffff',
        text_color: '#1f2937',
        font_family: 'Inter'
      };

      const stmt = db.prepare(`
        INSERT INTO company_customizations
        (company_id, primary_color, secondary_color, background_color, text_color, font_family)
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        defaultCustomization.company_id,
        defaultCustomization.primary_color,
        defaultCustomization.secondary_color,
        defaultCustomization.background_color,
        defaultCustomization.text_color,
        defaultCustomization.font_family
      );

      return res.json(defaultCustomization);
    }

    res.json(customization);
  } catch (error) {
    console.error('Erro ao buscar personalização:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar configurações de personalização
app.put('/api/company/customization', authenticateToken, (req, res) => {
  try {
    const user = req.user;
    const {
      logo_url,
      favicon_url,
      primary_color,
      secondary_color,
      background_color,
      text_color,
      font_family,
      custom_css
    } = req.body;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    // Verificar se já existe configuração
    const existing = db.prepare('SELECT id FROM company_customizations WHERE company_id = ?').get(user.company_id);

    if (existing) {
      // Atualizar existente
      const stmt = db.prepare(`
        UPDATE company_customizations
        SET logo_url = ?, favicon_url = ?, primary_color = ?, secondary_color = ?,
            background_color = ?, text_color = ?, font_family = ?, custom_css = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE company_id = ?
      `);

      stmt.run(
        logo_url || null,
        favicon_url || null,
        primary_color || '#1e40af',
        secondary_color || '#f59e0b',
        background_color || '#ffffff',
        text_color || '#1f2937',
        font_family || 'Inter',
        custom_css || null,
        user.company_id
      );
    } else {
      // Criar novo
      const stmt = db.prepare(`
        INSERT INTO company_customizations
        (company_id, logo_url, favicon_url, primary_color, secondary_color,
         background_color, text_color, font_family, custom_css)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        user.company_id,
        logo_url || null,
        favicon_url || null,
        primary_color || '#1e40af',
        secondary_color || '#f59e0b',
        background_color || '#ffffff',
        text_color || '#1f2937',
        font_family || 'Inter',
        custom_css || null
      );
    }

    // Retornar configuração atualizada
    const updated = db.prepare('SELECT * FROM company_customizations WHERE company_id = ?').get(user.company_id);

    res.json({
      success: true,
      message: 'Personalização atualizada com sucesso',
      data: updated
    });

  } catch (error) {
    console.error('Erro ao atualizar personalização:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar personalização pública (para aplicar no frontend)
app.get('/api/public/customization/:domain', (req, res) => {
  try {
    const { domain } = req.params;

    // Buscar empresa pelo domínio
    const company = db.prepare('SELECT id FROM companies WHERE domain = ?').get(domain);

    if (!company) {
      return res.status(404).json({ error: 'Empresa não encontrada' });
    }

    // Buscar personalização
    const customization = db.prepare(`
      SELECT logo_url, favicon_url, primary_color, secondary_color,
             background_color, text_color, font_family, custom_css
      FROM company_customizations
      WHERE company_id = ?
    `).get(company.id);

    if (!customization) {
      // Retornar configuração padrão
      return res.json({
        primary_color: '#1e40af',
        secondary_color: '#f59e0b',
        background_color: '#ffffff',
        text_color: '#1f2937',
        font_family: 'Inter'
      });
    }

    res.json(customization);
  } catch (error) {
    console.error('Erro ao buscar personalização pública:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Reset personalização para padrão
app.post('/api/company/customization/reset', authenticateToken, (req, res) => {
  try {
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    const stmt = db.prepare(`
      UPDATE company_customizations
      SET logo_url = NULL, favicon_url = NULL,
          primary_color = '#1e40af', secondary_color = '#f59e0b',
          background_color = '#ffffff', text_color = '#1f2937',
          font_family = 'Inter', custom_css = NULL,
          updated_at = CURRENT_TIMESTAMP
      WHERE company_id = ?
    `);

    stmt.run(user.company_id);

    const updated = db.prepare('SELECT * FROM company_customizations WHERE company_id = ?').get(user.company_id);

    res.json({
      success: true,
      message: 'Personalização resetada para o padrão',
      data: updated
    });

  } catch (error) {
    console.error('Erro ao resetar personalização:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// ===== CONFIGURAÇÃO DE UPLOAD DE ARQUIVOS =====

// Criar diretório de uploads se não existir
const uploadsDir = path.join(__dirname, 'public', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configuração do multer para upload de imagens
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Tipo de arquivo não permitido. Use apenas imagens (JPEG, PNG, GIF, WebP, SVG).'));
    }
  }
});

// API para upload de logo
app.post('/api/company/upload/logo', authenticateToken, upload.single('logo'), (req, res) => {
  try {
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    if (!req.file) {
      return res.status(400).json({ error: 'Nenhum arquivo foi enviado' });
    }

    // URL do arquivo
    const fileUrl = `/uploads/${req.file.filename}`;

    // Atualizar URL do logo na personalização
    const stmt = db.prepare(`
      UPDATE company_customizations
      SET logo_url = ?, updated_at = CURRENT_TIMESTAMP
      WHERE company_id = ?
    `);

    const result = stmt.run(fileUrl, user.company_id);

    if (result.changes === 0) {
      // Se não existe personalização, criar uma nova
      const insertStmt = db.prepare(`
        INSERT INTO company_customizations
        (company_id, logo_url, primary_color, secondary_color, background_color, text_color, font_family)
        VALUES (?, ?, '#1e40af', '#f59e0b', '#ffffff', '#1f2937', 'Inter')
      `);
      insertStmt.run(user.company_id, fileUrl);
    }

    res.json({
      success: true,
      message: 'Logo enviado com sucesso',
      data: {
        url: fileUrl,
        filename: req.file.filename
      }
    });

  } catch (error) {
    console.error('Erro ao fazer upload do logo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// API para upload de favicon
app.post('/api/company/upload/favicon', authenticateToken, upload.single('favicon'), (req, res) => {
  try {
    const user = req.user;

    if (!user.company_id) {
      return res.status(400).json({ error: 'Usuário deve estar associado a uma empresa' });
    }

    if (!req.file) {
      return res.status(400).json({ error: 'Nenhum arquivo foi enviado' });
    }

    // URL do arquivo
    const fileUrl = `/uploads/${req.file.filename}`;

    // Atualizar URL do favicon na personalização
    const stmt = db.prepare(`
      UPDATE company_customizations
      SET favicon_url = ?, updated_at = CURRENT_TIMESTAMP
      WHERE company_id = ?
    `);

    const result = stmt.run(fileUrl, user.company_id);

    if (result.changes === 0) {
      // Se não existe personalização, criar uma nova
      const insertStmt = db.prepare(`
        INSERT INTO company_customizations
        (company_id, favicon_url, primary_color, secondary_color, background_color, text_color, font_family)
        VALUES (?, ?, '#1e40af', '#f59e0b', '#ffffff', '#1f2937', 'Inter')
      `);
      insertStmt.run(user.company_id, fileUrl);
    }

    res.json({
      success: true,
      message: 'Favicon enviado com sucesso',
      data: {
        url: fileUrl,
        filename: req.file.filename
      }
    });

  } catch (error) {
    console.error('Erro ao fazer upload do favicon:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Inicializar banco e servidor
initDatabase().then(() => {
  app.listen(PORT, () => {
    console.log(`Servidor rodando na porta ${PORT}`);
    if (process.env.NODE_ENV === 'production') {
      console.log(`API disponível em: https://sollaragarden.onrender.com/api`);
    } else {
      console.log(`API disponível em: http://**************:${PORT}/api`);
    }
  });
}).catch(error => {
  console.error('Erro ao inicializar:', error);
});