# Correção do Problema de Duplicação de Dados

## 🐛 Problema Identificado:
O banco de dados estava inserindo dados iniciais toda vez que o servidor reiniciava, causando duplicação de imagens e vídeos.

## ✅ Correções Realizadas:

### 1. **Verificação de Dados Existentes** 
- Adicionado verificação se já existem dados antes de inserir
- Se dados já existem, pula a inserção inicial

### 2. **Constraints Únicas**
- `gallery_images.image_url` agora é UNIQUE 
- `videos.url` agora é UNIQUE
- `content_management` já tinha UNIQUE(section, field_name)

### 3. **Script de Limpeza**
- Criado `cleanup-duplicates.cjs` para remover dados duplicados existentes

## 🚀 Comandos para Executar no Servidor:

### 1. Commit e push das mudanças (no Windows):
```bash
git add .
git commit -m "Corrige duplicação de dados no banco"
git push origin main
```

### 2. No servidor Linux, executar:

```bash
# Parar o PM2
cd /opt/SollaraGarden
pm2 stop sollaragarden

# Fazer pull das mudanças
git pull origin main

# Instalar dependências (se necessário)
npm install
cd server && npm install && cd ..

# IMPORTANTE: Limpar dados duplicados ANTES de reiniciar
cd server
node cleanup-duplicates.cjs

# Voltar para a raiz e reiniciar
cd ..
pm2 restart sollaragarden

# Verificar logs
pm2 logs sollaragarden --lines 10
```

### 3. Verificar resultado:
```bash
# Acessar o site e verificar se não há mais duplicatas
curl -I http://sollara-garden.gruposalha.com.br

# Verificar logs do banco
pm2 logs sollaragarden | grep "Dados iniciais"
```

## 🔍 O que foi alterado:

### **server/database.cjs:**
1. **Verificação de dados existentes**: Antes de inserir, verifica se já existem dados
2. **Constraints únicas**: Adicionado UNIQUE em `image_url` e `url`
3. **Log melhorado**: Agora mostra se dados já existem

### **server/cleanup-duplicates.cjs:** (NOVO)
- Remove imagens duplicadas (mantém a primeira)
- Remove vídeos duplicados (mantém o primeiro)  
- Remove conteúdo duplicado (mantém o primeiro)
- Reordena índices da galeria
- Mostra estatísticas finais

## 📊 Resultado Esperado:
- ✅ Sem mais duplicação de dados
- ✅ Apenas um conjunto de imagens/vídeos iniciais
- ✅ Banco limpo e organizado
- ✅ Performance melhorada

## 🚨 Importante:
Execute o script de limpeza (`cleanup-duplicates.cjs`) **ANTES** de reiniciar o PM2 para garantir que os dados duplicados sejam removidos.

Se no futuro precisar resetar completamente o banco:
```bash
cd /opt/SollaraGarden/server
rm database.sqlite*
pm2 restart sollaragarden
``` 