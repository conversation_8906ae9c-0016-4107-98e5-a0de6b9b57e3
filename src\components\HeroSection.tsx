import React, { useRef, useEffect, useState } from 'react';
import FloatingParticles from './effects/FloatingParticles';
import { ApiService } from '@/lib/api';

interface HeroData {
  title?: string;
  subtitle?: string;
  description?: string;
  video_url?: string;
  video_type?: string;
  background_image?: string;
}

const HeroSection = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [scrollY, setScrollY] = useState(0);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [heroData, setHeroData] = useState<HeroData>({
    title: 'SOLLARA GARDEN',
    subtitle: 'Seu novo lar em Barra Mansa',
    description: 'CONFORTO, MODERNIDADE, SEGURANÇA E LAZER COMPLETO AO SEU ALCANCE',
    video_url: 'https://drive.google.com/uc?export=download&id=14jFcXML2KS1bawZ2P9V2_j7uoHNHrayp',
    video_type: 'file',
    background_image: ''
  });
  const [loading, setLoading] = useState(true);
  
  const scrollToVideoSection = () => {
    document.getElementById('video-section')?.scrollIntoView({ behavior: 'smooth' });
  };

  // Carregar dados da API
  useEffect(() => {
    const loadHeroData = async () => {
      try {
        const response = await ApiService.getPublicHeroContent();
        if (response && response.data) {
          setHeroData(prev => ({
            ...prev,
            ...response.data
          }));
        }
      } catch (error) {
        console.error('Erro ao carregar dados do hero:', error);
        // Manter dados padrão em caso de erro
      } finally {
        setLoading(false);
      }
    };

    loadHeroData();
  }, []);

  // Parallax effect
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Video autoplay with intersection observer
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && isVideoLoaded) {
            video.play().catch(console.log);
          } else {
            video.pause();
          }
        });
      },
      { threshold: 0.5 }
    );

    observer.observe(video);
    return () => observer.disconnect();
  }, [isVideoLoaded]);

  // Função para converter URL do YouTube para embed
  const getYouTubeEmbedUrl = (url: string) => {
    let videoId = '';
    
    if (url.includes('youtube.com/shorts/')) {
      const match = url.match(/shorts\/([a-zA-Z0-9_-]+)/);
      videoId = match ? match[1] : '';
    } else if (url.includes('youtu.be/')) {
      const match = url.match(/youtu\.be\/([a-zA-Z0-9_-]+)/);
      videoId = match ? match[1] : '';
    } else if (url.includes('youtube.com/watch')) {
      const match = url.match(/[?&]v=([a-zA-Z0-9_-]+)/);
      videoId = match ? match[1] : '';
    }
    
    if (videoId) {
      return `https://www.youtube-nocookie.com/embed/${videoId}?autoplay=1&mute=1&controls=0&rel=0&modestbranding=1&playsinline=1&loop=1&playlist=${videoId}`;
    }
    
    return url;
  };

  if (loading) {
    return (
      <section className="hero-section relative min-h-screen flex items-center justify-center overflow-hidden bg-black">
        <div className="absolute inset-0 bg-gradient-to-br from-luxury-red/30 via-black/50 to-luxury-gold/20" />
        <div className="relative z-20 flex items-center justify-center">
          <div className="w-16 h-16 border-4 border-luxury-gold border-t-transparent rounded-full animate-spin"></div>
        </div>
      </section>
    );
  }

  return (
    <section 
      ref={heroRef}
      className="hero-section relative min-h-screen flex items-center justify-center overflow-hidden bg-black"
      style={heroData.background_image ? {
        backgroundImage: `url(${heroData.background_image})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      } : {}}
    >
      {/* Video/Background */}
      <div className="absolute inset-0 z-0">
        {heroData.video_url && heroData.video_type === 'youtube' ? (
          <iframe
            className="w-full h-full object-cover"
            src={getYouTubeEmbedUrl(heroData.video_url)}
            allow="autoplay; encrypted-media"
            allowFullScreen
            style={{ pointerEvents: 'none' }}
          />
        ) : heroData.video_url && heroData.video_type === 'file' ? (
          <video
            ref={videoRef}
            className="w-full h-full object-cover opacity-80"
            autoPlay
            muted
            loop
            playsInline
            onLoadedData={() => setIsVideoLoaded(true)}
            onError={() => console.log('Error loading video')}
          >
            <source src={heroData.video_url} type="video/mp4" />
          </video>
        ) : heroData.background_image ? (
          <div className="w-full h-full bg-cover bg-center bg-no-repeat opacity-80" />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-luxury-brown via-black to-luxury-gold opacity-80" />
        )}
        
        {/* Overlay with gradients */}
        <div className="absolute inset-0 bg-gradient-to-br from-luxury-red/30 via-black/50 to-luxury-gold/20" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-black/30" />
      </div>

      <FloatingParticles />
      
      {/* Content */}
      <div className="relative z-20 text-center px-4 md:px-8 max-w-6xl mx-auto">
        <div className="space-y-8 md:space-y-12">
          {/* Logo - Significantly increased size for mobile */}
          <div className="mb-6 md:mb-8">
            <div className="relative inline-block">
              <img 
                src="/lovable-uploads/c67509dc-b8fd-4b63-a711-7737584ea409.png" 
                alt="Sollara Garden Logo"
                className="mx-auto h-96 sm:h-[28rem] md:h-[32rem] lg:h-[36rem] w-auto drop-shadow-2xl"
              />
            </div>
          </div>

          {/* Dynamic Title and Content */}
          <div className="space-y-4">
            {heroData.title && (
              <div className="relative">
                <h1 className="font-sf-pro text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 mb-4">
                  {heroData.title}
                </h1>
              </div>
            )}
            {heroData.subtitle && (
              <div className="relative">
                <h2 className="font-sf-pro text-xl sm:text-2xl md:text-3xl lg:text-4xl font-semibold leading-tight text-white/90 mb-4">
                  {heroData.subtitle}
                </h2>
              </div>
            )}
            {heroData.description && (
              <div className="relative">
                <p className="font-sf-pro text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold leading-tight text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600">
                  {heroData.description}
                </p>
              </div>
            )}
            
            {/* CTA Button with golden theme */}
            <button
              onClick={scrollToVideoSection}
              className="relative overflow-hidden bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 hover:from-yellow-500 hover:via-yellow-600 hover:to-yellow-700 text-black font-bold py-4 px-8 rounded-2xl text-lg md:text-xl transition-all duration-300 hover:shadow-lg transform hover:scale-105 border-2 border-yellow-500/50"
            >
              <span className="relative z-10">APRESENTAÇÃO EXCLUSIVA</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
