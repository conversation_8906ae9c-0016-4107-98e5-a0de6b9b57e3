const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

interface ApiResponse<T = any> {
  success?: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export class ApiService {
  private static getAuthHeaders() {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };
  }

  private static async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Erro na API' }));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }
    return response.json();
  }

  // Submissões do formulário
  static async getSubmissions(): Promise<any[]> {
    const response = await fetch(`${API_BASE_URL}/admin/submissions`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  static async updateSubmissionStatus(id: number, status: string): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/submissions/${id}/status`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ status }),
    });
    return this.handleResponse(response);
  }

  static async deleteSubmission(id: number): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/submissions/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Vídeos
  static async getVideos(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/admin/videos`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  static async getPublicVideos(): Promise<ApiResponse<any[]>> {
    const response = await fetch(`${API_BASE_URL}/videos`);
    return this.handleResponse(response);
  }

  static async createVideo(videoData: any): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/videos`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(videoData),
    });
    return this.handleResponse(response);
  }

  static async updateVideo(id: number, videoData: any): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/videos/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(videoData),
    });
    return this.handleResponse(response);
  }

  static async deleteVideo(id: number): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/videos/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Galeria de Imagens
  static async getGalleryImages(): Promise<ApiResponse<any[]>> {
    const response = await fetch(`${API_BASE_URL}/admin/gallery`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  static async getPublicGalleryImages(): Promise<ApiResponse<any[]>> {
    const response = await fetch(`${API_BASE_URL}/gallery`);
    return this.handleResponse(response);
  }

  static async createGalleryImage(imageData: any): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/gallery`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(imageData),
    });
    return this.handleResponse(response);
  }

  static async updateGalleryImage(id: number, imageData: any): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/gallery/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(imageData),
    });
    return this.handleResponse(response);
  }

  static async deleteGalleryImage(id: number): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/gallery/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Configurações
  static async getSettings(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/settings`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  static async updateSettings(settings: any): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/settings`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(settings),
    });
    return this.handleResponse(response);
  }

  static async getPublicSettings(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/settings`);
    return this.handleResponse(response);
  }

  // Conteúdo do rodapé
  static async getFooterContent(): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE_URL}/admin/footer`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  static async updateFooterContent(footerData: any): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/footer`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(footerData),
    });
    return this.handleResponse(response);
  }

  static async getPublicFooterContent(): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE_URL}/footer`);
    return this.handleResponse(response);
  }

  // Conteúdo da seção hero/principal
  static async getHeroContent(): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE_URL}/admin/hero`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  static async updateHeroContent(heroData: any): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/hero`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(heroData),
    });
    return this.handleResponse(response);
  }

  static async getPublicHeroContent(): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE_URL}/hero`);
    return this.handleResponse(response);
  }

  // APIs de Personalização WhiteLabel
  static async getCustomization(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/company/customization`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  static async updateCustomization(customizationData: any): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/company/customization`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(customizationData),
    });
    return this.handleResponse(response);
  }

  static async resetCustomization(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/company/customization/reset`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  static async getPublicCustomization(domain: string): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/public/customization/${domain}`);
    return this.handleResponse(response);
  }

  static async uploadLogo(file: File): Promise<ApiResponse> {
    const formData = new FormData();
    formData.append('logo', file);

    const response = await fetch(`${API_BASE_URL}/company/upload/logo`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: formData,
    });
    return this.handleResponse(response);
  }

  static async uploadFavicon(file: File): Promise<ApiResponse> {
    const formData = new FormData();
    formData.append('favicon', file);

    const response = await fetch(`${API_BASE_URL}/company/upload/favicon`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: formData,
    });
    return this.handleResponse(response);
  }

  // APIs de Relatórios Super Admin
  static async getMonthlyReport(month?: number, year?: number): Promise<ApiResponse> {
    const params = new URLSearchParams();
    if (month) params.append('month', month.toString());
    if (year) params.append('year', year.toString());

    const response = await fetch(`${API_BASE_URL}/super-admin/reports/monthly?${params}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  static async getUsageReport(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/super-admin/reports/usage`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  static async getChurnReport(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/super-admin/reports/churn`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }
}